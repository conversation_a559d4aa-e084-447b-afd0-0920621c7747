package cn.chinaunicom.paas.codeassistant.intellijextension.service;

import com.intellij.openapi.components.Service;
import com.intellij.openapi.project.Project;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/8/11 09:55
 */
@Service(Service.Level.PROJECT)
public class DiagnosticService {
    private final Project project;

    public DiagnosticService(Project project) {
        this.project = project;
    }

    public static DiagnosticService getInstance(Project project) {
        return project.getService(DiagnosticService.class);
    }

    /**
     * 获取指定文件的诊断信息
     * @param filePath 文件路径
     * @return 诊断信息列表，每个元素包含问题位置、描述和代码片段
     */
    public List<DiagnosticInfo> getDiagnostic(String filePath) {
    }

}