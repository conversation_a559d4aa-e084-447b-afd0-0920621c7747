package cn.chinaunicom.paas.codeassistant.intellijextension.activity;

import cn.chinaunicom.paas.codeassistant.intellijextension.git.GitRepositoryServiceManager;
import cn.chinaunicom.paas.codeassistant.intellijextension.listener.EditorSelectionListener;
import cn.chinaunicom.paas.codeassistant.intellijextension.listener.ThemeListener;
import cn.chinaunicom.paas.codeassistant.intellijextension.process.ProcessClient;
import cn.chinaunicom.paas.codeassistant.intellijextension.process.ProcessMessenger;
import cn.chinaunicom.paas.codeassistant.intellijextension.server.PluginServer;
import cn.chinaunicom.paas.codeassistant.intellijextension.service.DiagnosticService;
import cn.chinaunicom.paas.codeassistant.intellijextension.service.DiagnosticService.DiagnosticInfo;
import cn.chinaunicom.paas.codeassistant.intellijextension.service.PluginCoreService;
import cn.chinaunicom.paas.codeassistant.intellijextension.state.UniqueIdState;
import com.intellij.ide.plugins.PluginManagerCore;
import com.intellij.openapi.Disposable;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.EditorFactory;
import com.intellij.openapi.extensions.PluginDescriptor;
import com.intellij.openapi.extensions.PluginId;
import com.intellij.openapi.project.DumbAware;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.startup.ProjectActivity;
import com.intellij.openapi.util.Disposer;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/30 15:22
 **/

public class PluginStartupActivity implements ProjectActivity, Disposable, DumbAware {

    private static final Logger LOG = Logger.getInstance(PluginStartupActivity.class);
    private static ExecutorService executorService;
    private PluginCoreService pluginCoreService;

    @Override
    public @Nullable Object execute(@NotNull Project project, @NotNull Continuation<? super Unit> continuation) {
        LOG.info("联通云智能编程助手启动");
        // 注册当前对象作为可释放资源
        Disposer.register(project, this);

        // 在后台线程中初始化插件
        ApplicationManager.getApplication().executeOnPooledThread(() -> {
            try {
                initializePlugin(project);  // 确保此方法是线程安全的
            } catch (Exception e) {
                // 处理初始化过程中可能出现的异常
                LOG.warn("初始化插件异常：", e);
            }
        });
        return Unit.INSTANCE;
    }


    /**
     * 初始化插件
     * 该方法在插件被加载时调用，用于完成插件的初始化工作
     * 包括生成插件唯一标识，注册卸载监听器，启动嵌入式服务器，以及注册编辑器文本选中事件监听器
     *
     */
    private void initializePlugin(Project project) {
        // 获取插件核心服务实例，用于后续的插件功能实现
        this.pluginCoreService = PluginCoreService.getInstance(project);

        //初始化git
        GitRepositoryServiceManager.init(project);
        executorService = Executors.newCachedThreadPool();
        // 创建进程通信客户端
        Future<?> parentTask = executorService.submit(() -> {
            executorService.submit(() -> {

                // 启动嵌入式服务器，为插件提供网络服务功能
                PluginServer.getInstance().startServer(true);

                // 注册编辑器文本选中事件监听器，用于监听用户在编辑器中的文本选中操作
                EditorSelectionListener editorSelectionListener = new EditorSelectionListener();
                // 向编辑器工厂的事件多播器添加选中监听器，使得在文本选中变化时能够触发相应处理
                EditorFactory.getInstance().getEventMulticaster().addSelectionListener(
                        editorSelectionListener,
                        this);

                //监听主题变化
                ThemeListener.registerThemeChangeListener();
            });

            ProcessClient processClient = new ProcessClient(project, project.getBasePath(), pluginCoreService);
            pluginCoreService.setProcessClient(processClient);

            executorService.submit(() -> {
                String[] workspacePaths;
                if (project.getBasePath() != null) {
                    workspacePaths = new String[]{project.getBasePath()};
                } else {
                    workspacePaths = new String[0]; // Empty array
                }
                // 将列表转换为数组
                pluginCoreService.setWorkspacePaths(workspacePaths);
            });

            executorService.submit(() -> {
                String myPluginId = "CUCloudAICodeAssistant";
                PluginDescriptor pluginDescriptor = PluginManagerCore.getPlugin(PluginId.getId(myPluginId));

                if (pluginDescriptor == null) {
                    throw new RuntimeException("Plugin not found");
                }

                Path pluginPath = pluginDescriptor.getPluginPath();
                String osName = System.getProperty("os.name").toLowerCase();
                String os;
                if (osName.contains("mac") || osName.contains("darwin")) {
                    os = "darwin";
                } else if (osName.contains("win")) {
                    os = "win32";
                } else if (osName.contains("nix") || osName.contains("nux") || osName.contains("aix")) {
                    os = "linux";
                } else {
                    os = "linux";
                }

                String osArch = System.getProperty("os.arch").toLowerCase();
                String arch;
                if (osArch.contains("aarch64") || (osArch.contains("arm") && osArch.contains("64"))) {
                    arch = "arm64";
                } else if (osArch.contains("amd64") || osArch.contains("x86_64")) {
                    arch = "x64";
                } else {
                    arch = "x64";
                }

                String target = os + "-" + arch;
                System.out.println("确认 OS: " + os + ", Arch: " + arch);

                Path corePath = pluginPath.resolve("core");
                Path targetPath = corePath.resolve(target);
                Path continueCorePath = targetPath.resolve("cucloud-binary" + (os.equals("win32") ? ".exe" : ""));

                ProcessMessenger processMessenger = new ProcessMessenger(project, continueCorePath.toString(), processClient);
                pluginCoreService.setProcessMessenger(processMessenger);
            });

            //for debug
            executorService.submit(() -> {

              ApplicationManager.getApplication().invokeLater(() -> {
                DiagnosticService diagnosticService = DiagnosticService.getInstance(project);
                File diagnosticFile = new File("/Users/<USER>/tmp/logs/diagnostic.txt");
                final File diagnosticReadyFile = new File(diagnosticFile.getParent(), diagnosticFile.getName() + ".ready");

                while (true) {
                  if (diagnosticFile.exists() && !diagnosticReadyFile.exists()) {
                    try {
                      String filePath = new String(Files.readAllBytes(diagnosticFile.toPath()));
                      List<DiagnosticInfo> diagnosticInfo = diagnosticService.getDiagnostic(filePath);
                      LOG.info("Diagnostic info: " + diagnosticInfo);
                      Files.move(diagnosticFile.toPath(), diagnosticReadyFile.toPath());
                    } catch (IOException e) {
                      LOG.warn("Failed to read diagnostic file: " + e.getMessage());
                    }
                  }
                  try {
                    Thread.sleep(1000);
                  } catch (InterruptedException e) {
                    LOG.warn("Thread interrupted: " + e.getMessage());
                  }
                }

              });
            });
        });
//        parentTask.get();
//        executorService.shutdown();
    }


    /**
     * 生成或获取唯一标识符
     *
     * 本方法用于在系统中获取一个唯一的标识符（uniqueId）
     * 如果系统中尚未设置唯一标识符，则生成一个新的唯一标识符并存储之
     * 此方法确保在不同执行实例间共享同一唯一标识符，通常用于需要全局唯一标识的场景
     */
    private void newUniqueId() {
        // 获取唯一标识符服务实例
        UniqueIdState uniqueIdState = UniqueIdState.getInstance();

        // 尝试从服务中获取现有唯一标识符
        String uniqueId = uniqueIdState.getUniqueId();

        // 如果获取的唯一标识符为null
        if (uniqueId == null) {
            // 生成新的唯一标识符
            uniqueId = UUID.randomUUID().toString();

            // 设置新的唯一标识符到服务中，以便后续使用和共享
            uniqueIdState.setUniqueId(uniqueId);

            // 打印唯一标识符，通常用于调试或日志记录
            System.out.println("唯一标识：" + uniqueId);

            // 首次安装的其他处理
        }
    }


    @Override
    public void dispose() {
        PluginServer.getInstance().forceStopServer();
        if (executorService != null) {
            executorService.shutdown();
        }
    }

}
