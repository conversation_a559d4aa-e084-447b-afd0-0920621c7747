package cn.chinaunicom.paas.codeassistant.intellijextension.toolwindow;

import cn.chinaunicom.paas.codeassistant.intellijextension.MyBundle;
import cn.chinaunicom.paas.codeassistant.intellijextension.actions.OpenSettingsAction;
import cn.chinaunicom.paas.codeassistant.intellijextension.auth.Account;
import cn.chinaunicom.paas.codeassistant.intellijextension.auth.LoginService;
import cn.chinaunicom.paas.codeassistant.intellijextension.base.Command;
import cn.chinaunicom.paas.codeassistant.intellijextension.code.CodeDiffManager;
import cn.chinaunicom.paas.codeassistant.intellijextension.file.FileDownloader;
import cn.chinaunicom.paas.codeassistant.intellijextension.file.FileManager;
import cn.chinaunicom.paas.codeassistant.intellijextension.git.GitInfoUtils;
import cn.chinaunicom.paas.codeassistant.intellijextension.git.GitRepositoryService;
import cn.chinaunicom.paas.codeassistant.intellijextension.manager.GuiInitManager;
import cn.chinaunicom.paas.codeassistant.intellijextension.manager.TerminalManager;
import cn.chinaunicom.paas.codeassistant.intellijextension.record.CommandCodeInfo;
import cn.chinaunicom.paas.codeassistant.intellijextension.record.GuiCommand;
import cn.chinaunicom.paas.codeassistant.intellijextension.record.MessageResult;
import cn.chinaunicom.paas.codeassistant.intellijextension.record.Position;
import cn.chinaunicom.paas.codeassistant.intellijextension.service.PluginCoreService;
import cn.chinaunicom.paas.codeassistant.intellijextension.state.PluginInfoState;
import cn.chinaunicom.paas.codeassistant.intellijextension.ui.NotificationManager;
import cn.chinaunicom.paas.codeassistant.intellijextension.utils.EditorUtils;
import cn.chinaunicom.paas.codeassistant.intellijextension.utils.ToolWindowUtils;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;
import com.intellij.ide.BrowserUtil;
import com.intellij.openapi.application.ApplicationManager;
import com.intellij.openapi.command.WriteCommandAction;
import com.intellij.openapi.diagnostic.Logger;
import com.intellij.openapi.editor.Editor;
import com.intellij.openapi.fileEditor.FileEditorManager;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.util.Disposer;
import com.intellij.ui.jcef.JBCefBrowser;
import com.intellij.ui.jcef.JBCefBrowserBase;
import com.intellij.ui.jcef.JBCefClient;
import com.intellij.ui.jcef.JBCefJSQuery;
import org.cef.browser.CefBrowser;
import org.cef.handler.CefLoadHandlerAdapter;

import javax.swing.JComponent;
import javax.swing.SwingUtilities;
import java.io.IOException;
import java.lang.reflect.Type;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Queue;
import java.util.UUID;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @Date 2024/8/22 13:35
 **/

public class PluginBrowser {
    private static final Logger LOG = Logger.getInstance(PluginBrowser.class);
    private final Project project;
    private final String url;
    private JBCefBrowser browser;
    private boolean guiMounted = false;
    private final Gson gson = new Gson();
    private final FileManager fileManager;
    private final PluginCoreService pluginCoreService;

    public PluginBrowser(Project project, String url) {
        this.project = project;
        this.url = url;
        this.fileManager = new FileManager(project);
        this.pluginCoreService = PluginCoreService.getInstance(project);
        init();
    }

    //直接转发到进程
    private static final List<String> passToProcess = Arrays.asList(
            Command.history_load,
            Command.history_save,
            Command.history_list,
            Command.history_delete,
            Command.history_clear,
            Command.context_getContextItems,
            Command.context_loadContextItems,
            Command.context_loadSubmenuItems,
            Command.mcp_list,
            Command.mcp_callTool,
            Command.mcp_togglerServerDisabled,
            Command.mcp_deleteServer,
            Command.mcp_restartServer,
            Command.mcp_addLocalServer,
            Command.mcp_addRemoteServer,
            Command.mcp_editConfig,
            Command.mcp_updateConfig,
            Command.mcp_getConfig,
            Command.aiRules_open,
            Command.aiRules_insert,
            Command.aiRules_get,
            Command.index_setPaused,
            Command.index_forceReIndex,
            Command.index_clearIndex
    );

    private void init() {
//        var osName = System.getProperty("os.name").toLowerCase();
//        String os;
//        if (osName.contains("mac") || osName.contains("darwin")) {
//            os = "darwin";
//        } else if (osName.contains("win")) {
//            os = "win32";
//        } else if (osName.contains("nix") || osName.contains("nux") || osName.contains("aix")) {
//            os = "linux";
//        } else {
//            os = "linux";
//        }

        this.browser = JBCefBrowser.createBuilder().build();
        this.browser.getJBCefClient().setProperty(JBCefClient.Properties.JS_QUERY_POOL_SIZE, "200");
        this.browser.loadURL(url);
        System.out.println("browser: " + url);
        Disposer.register(project, this.browser);

        // 监听浏览器事件
        JBCefJSQuery myJSQueryOpenInBrowser = JBCefJSQuery.create((JBCefBrowserBase) browser);
        myJSQueryOpenInBrowser.addHandler(msg -> {
            var json = JsonParser.parseString(msg).getAsJsonObject();
            System.out.println("GUI事件json: " + json);
            var source = json.get("source") != null ? json.get("source").getAsString() : null;
            var commandId = json.get("commandId") != null ? json.get("commandId").getAsString() : null;
            var command = json.get("command") != null ? json.get("command").getAsString() : null;
            JsonObject payload = json.get("payload") != null ? json.getAsJsonObject("payload") : null;

            if (command == null) {
                return null;
            }

            // 创建 respond 回调函数，用于将数据发送回前端
            Consumer<Object> respond = (responseData) -> {
                JsonObject data = gson.toJsonTree(responseData).getAsJsonObject();
                sendMessageResult("Core", command, commandId, data);
            };
            // 调用 request 方法，将数据发送到后端进程,注册回调 respond，等待核心模块返回数据后执行
            if (passToProcess.contains(command)) {
                if (pluginCoreService.getProcessMessenger() != null) {
                    pluginCoreService.getProcessMessenger().request(source, commandId,
                            command, payload, respond);
                    return null;
                }
            }

            try {
                //其余命令
                switch (command) {
                    case Command.log_info -> {
                        if (payload != null && !payload.isEmpty() && !payload.get("message").isJsonObject()) {
                            var message = payload.get("message") != null ? payload.get("message").getAsString() : null;
                            LOG.info(message);
                        }
                    }
                    case Command.log_debug -> {
                        if (payload != null && !payload.isEmpty() && !payload.get("message").isJsonObject()) {
                            var message = payload.get("message") != null ? payload.get("message").getAsString() : null;
                            LOG.debug(message);
                        }
                    }
                    case Command.log_warn -> {
                        if (payload != null && !payload.isEmpty() && !payload.get("message").isJsonObject()) {
                            var message = payload.get("message") != null ? payload.get("message").getAsString() : null;
                            LOG.warn(message);
                        }
                    }
                    case Command.log_error -> {
                        if (payload != null && !payload.isEmpty() && !payload.get("message").isJsonObject()) {
                            var message = payload.get("message") != null ? payload.get("message").getAsString() : null;
                            LOG.error(message);
                        }
                    }

                    case Command.file_create -> {
                        if (payload != null && !payload.isEmpty()) {
                            var content = payload.get("content") != null ? payload.get("content").getAsString() : null;
                            var language = payload.get("language") != null ? payload.get("language").getAsString() : null;
                            var filename = payload.get("filename") != null ? payload.get("filename").getAsString() : null;
                            if (content != null) {
                                ApplicationManager.getApplication().invokeLater(() -> {
                                    fileManager.createNewFile(content, language, filename);
                                });
                            }
                        }
                    }
                    case Command.file_open -> {
                        if (payload != null && !payload.isEmpty()) {
                            var path = payload.get("path") != null ? payload.get("path").getAsString() : null;
                            var codeInfo = payload.get("range") != null ? payload.get("range").getAsJsonObject() : null;
                            if (path != null) {
                                ApplicationManager.getApplication().invokeLater(() -> {
                                    fileManager.openFile(path);
                                    if (codeInfo != null && !codeInfo.isEmpty()) {
                                        CommandCodeInfo commandCodeInfo = gson.fromJson(codeInfo, CommandCodeInfo.class);
                                        Position startPosition = commandCodeInfo.start();
                                        Position endPosition = commandCodeInfo.end();
                                        fileManager.setSelection(startPosition, endPosition);
                                    }
                                });
                            }
                        }
                    }
                    case Command.file_edit -> {
                        if (payload != null && !payload.isEmpty()) {
                            var path = payload.get("path") != null ? payload.get("path").getAsString() : null;
                            var content = payload.get("content") != null ? payload.get("content").getAsString() : null;
                            var start = payload.get("start") != null ? payload.get("start").getAsJsonObject() : null;
                            var end = payload.get("end") != null ? payload.get("end").getAsJsonObject() : null;
                            if (path != null && content != null) {
                                Position startPosition;
                                Position endPosition;
                                if (start != null) {
                                    startPosition = gson.fromJson(start, Position.class);
                                } else {
                                    startPosition = null;
                                }
                                if (end != null) {
                                    endPosition = gson.fromJson(end, Position.class);
                                } else {
                                    endPosition = null;
                                }
                                ApplicationManager.getApplication().invokeLater(() -> {
                                    fileManager.editFile(path, content, startPosition, endPosition);
                                });
                            }
                        }
                    }
                    case Command.file_delete -> {
                        if (payload != null && !payload.isEmpty()) {
                            JsonArray filesArray = payload.get("files") != null ? payload.getAsJsonArray("files") : null;
                            if (filesArray != null) {
                                String[] files = new String[filesArray.size()];
                                for (int i = 0; i < filesArray.size(); i++) {
                                    files[i] = filesArray.get(i).getAsString();
                                }
                                ApplicationManager.getApplication().invokeLater(() -> {
                                    fileManager.deleteFiles(files);
                                });
                            }
                        }
                    }
                    case Command.file_download -> {
                        if (payload != null && !payload.isEmpty()) {
                            var url = payload.get("url") != null ? payload.get("url").getAsString() : null;
                            if (url != null) {
                                FileDownloader fileDownloader = new FileDownloader();
                                String downloadedFilePath = null;
                                try {
                                    downloadedFilePath = fileDownloader.downloadFile(url);
                                } catch (IOException e) {
                                    System.out.println("下载文件失败，url:" + url);
                                }
                                sendMessageResult(null, Command.file_download, commandId, downloadedFilePath);
                            }
                        }
                    }
                    case Command.file_readFile -> {
                        if (payload != null && !payload.isEmpty()) {
                            var path = payload.get("path") != null ? payload.get("path").getAsString() : null;
                            if (path != null) {
                                String content = fileManager.readFile(path).get();
                                sendMessageResult(null, Command.file_readFile, commandId, content);
                            }
                        }
                    }
                    case Command.file_exists -> {
                        if (payload != null && !payload.isEmpty()) {
                            var path = payload.get("path") != null ? payload.get("path").getAsString() : null;
                            if (path != null) {
                                ApplicationManager.getApplication().invokeLater(() -> {
                                    sendMessageResult(null, Command.file_exists, commandId, fileManager.exits(path));
                                });
                            }
                        }
                    }
                    case Command.file_readDirectory -> {
                        if (payload != null && !payload.isEmpty()) {
                            var dir = payload.get("dir") != null ? payload.get("dir").getAsString() : null;
                            var prefix = payload.get("prefix") != null ? payload.get("prefix").getAsString() : "";
                            var options = payload.get("options") != null ? payload.getAsJsonObject("options") : null;
                            if (dir != null) {
                                ApplicationManager.getApplication().invokeLater(() -> {
                                    List<List<Object>> directory;
                                    if (options != null) {
                                        Type listType = new TypeToken<ArrayList<String>>(){}.getType();
                                        List<String> ignoreDirs = gson.fromJson(
                                                options.getAsJsonArray("ignoreDirs"),
                                                listType
                                        );
                                        List<String> ignoreFileTypes = gson.fromJson(
                                                options.getAsJsonArray("ignoreFileTypes"),
                                                listType
                                        );
                                        directory = fileManager.readDirectory(dir, prefix, ignoreDirs, ignoreFileTypes);
                                    } else {
                                        directory = fileManager.readDirectory(dir, prefix, null, null);
                                    }
                                    sendMessageResult(null, Command.file_readDirectory, commandId, directory);
                                });
                            }
                        }
                    }
                    case Command.feature_openSettings -> new OpenSettingsAction().openSettings(project);
                    case Command.feature_openHelp -> BrowserUtil.browse(MyBundle.get("url.help"));
                    case Command.feature_openUrl -> {
                        if (payload != null && !payload.isEmpty()) {
                            var url = payload.get("url") != null ? payload.get("url").getAsString() : null;
                            if (url != null) {
                                BrowserUtil.browse(url);
                            }
                        }
                    }
                    case Command.feature_insertCode -> {
                        if (payload != null && !payload.isEmpty()) {
                            //var language = payload.get("language") != null ? payload.get("language").getAsString() : null;
                            var content = payload.get("content") != null ? payload.get("content").getAsString() : null;
                            var codeInfo = payload.get("codeInfo") != null ? payload.get("codeInfo").getAsJsonObject() : null;
                            ApplicationManager.getApplication().invokeLater(() -> {
                                if (codeInfo != null && !codeInfo.isEmpty()) {
                                    CommandCodeInfo commandCodeInfo = gson.fromJson(codeInfo, CommandCodeInfo.class);
                                    Position startPosition = commandCodeInfo.start();
                                    Position endPosition = commandCodeInfo.end();
                                    String filePath = commandCodeInfo.filePath();
                                    if (startPosition != null && endPosition != null && filePath != null) {
                                        EditorUtils.replaceTextInRange(project, startPosition, endPosition, content, filePath);
                                    }
                                } else {
                                    EditorUtils.insertTextAtCaret(project, content);
                                }
                            });
                        }
                    }
                    case Command.feature_showDiff -> {
                        if (payload != null && !payload.isEmpty()) {
                            //var language = payload.get("language") != null ? payload.get("language").getAsString() : null;
                            var content = payload.get("content") != null ? payload.get("content").getAsString() : null;
                            var codeInfo = payload.get("codeInfo") != null ? payload.get("codeInfo").getAsJsonObject() : null;
                            if (codeInfo != null && !codeInfo.isEmpty()) {
                                CommandCodeInfo commandCodeInfo = gson.fromJson(codeInfo, CommandCodeInfo.class);
                                String text = commandCodeInfo.text();
                                if (text != null && !text.isBlank()) {
                                    CodeDiffManager codeDiffManager = new CodeDiffManager(project, commandCodeInfo);
                                    codeDiffManager.showCodeDiff(text, content);
                                }
                            }
                        }
                    }
                    case Command.feature_createTerminal -> {
                        TerminalManager terminalManager = new TerminalManager(project);
                        terminalManager.createTerminal();
                    }
                    case Command.feature_insertToTerminal -> {
                        if (payload != null && !payload.isEmpty()) {
                            var commandText = payload.get("command") != null ? payload.get("command").getAsString() : null;
                            if (commandText != null) {
                                TerminalManager terminalManager = new TerminalManager(project);
                                terminalManager.insertToTerminal(commandText);
                            }
                        }
                    }
                    case Command.feature_showToast -> {
                        if (payload != null && !payload.isEmpty()) {
                            var type = payload.get("type") != null ? payload.get("type").getAsString() : null;
                            var title = payload.get("title") != null ? payload.get("title").getAsString() : null;
                            var message = payload.get("message") != null ? payload.get("message").getAsString() : null;
                            if (type != null && message != null) {
                                if (type.equals("warn")) {
                                    NotificationManager notification = new NotificationManager(project);
                                    notification.showWarning(title, message);
                                } else if (type.equals("error")) {
                                    NotificationManager notification = new NotificationManager(project);
                                    notification.showError(title, message);
                                } else {
                                    NotificationManager notification = new NotificationManager(project);
                                    notification.showInfo(title, message);
                                }
                            }

                        }
                    }
                    case Command.feature_cancelEditorSelection ->
                            ApplicationManager.getApplication().invokeLater(() -> {
                                Editor editor = FileEditorManager.getInstance(project).getSelectedTextEditor();
                                EditorUtils.clearSelection(project, editor, false);
                            });
                    case Command.feature_getErrors -> {

                    }
                    case Command.event_onAddIDEContext -> {
                        if (payload != null && !payload.isEmpty()) {
                            var allowOpenHistory = payload.get("allowOpenHistory") != null ?
                                    payload.get("allowOpenHistory").getAsBoolean() : null;
                            var allowClearHistory = payload.get("allowClearHistory") != null ?
                                    payload.get("allowClearHistory").getAsBoolean() : null;
                        }
                    }

                    case Command.lifecycle_onGuiMounted -> {
                        //GUI已加载完成
                        guiMounted = true;
                        //gui初始化,发送主题信息
                        JsonObject theme = new JsonObject();
                        theme.addProperty("colorTheme", GuiInitManager.getIdeTheme());
                        sendToWebview(null, Command.theme_onThemeChange, null, theme);

                        Queue<GuiCommand> commandQueue = pluginCoreService.getCommandQueue();
                        while (!commandQueue.isEmpty()) {
                            GuiCommand guiCommand = commandQueue.poll();
                            sendToWebview(guiCommand);
                        }

                        //保存guiVersion信息
                        if (payload != null) {
                            var version = payload.get("version") != null ? payload.get("version").getAsString() : null;
                            if (version != null) {
                                PluginInfoState pluginInfoState = PluginInfoState.getInstance();
                                pluginInfoState.setGuiVersion(version);
                                pluginInfoState.saveState();
                            }
                        }

                        // 获取当前项目的当前编辑器实例。
                        Editor editor = FileEditorManager.getInstance(project).getSelectedTextEditor();
                        WriteCommandAction.runWriteCommandAction(project, () -> {
                            if (editor != null && editor.getSelectionModel().hasSelection()) {
                                ToolWindowUtils.sendSelectionToWebView(editor);
                            }
                        });
                    }

                    case Command.query_ideInfo -> ApplicationManager.getApplication().invokeLater(() -> {
                        JsonObject ideInfo = GuiInitManager.getIdeInfo(project);
                        sendMessageResult(null, Command.query_ideInfo, commandId, ideInfo);
                    });
                    case Command.query_gitInfo -> ApplicationManager.getApplication().invokeLater(() -> {
                        JsonObject gitInfo = GitInfoUtils.getGitInfoAsPayload(project);
                        sendMessageResult(null, Command.query_gitInfo, commandId, gitInfo);
                    });
                    case Command.query_workspaceDirs -> {
                        String[] workspaceDirectories = pluginCoreService.getProcessClient().workspaceDirectories();
                        sendMessageResult(null, Command.query_workspaceDirs, commandId, workspaceDirectories);
                    }
                    case Command.query_directoryTree -> {
                        if (payload != null && !payload.isEmpty()) {
                            var dir = payload.get("dir") != null ? payload.get("dir").getAsString() : null;
                            var prefix = payload.get("prefix") != null ? payload.get("prefix").getAsString() : "";
                            var options = payload.get("options") != null ? payload.getAsJsonObject("options") : null;
                            if (dir != null) {
                                Path dirPath = Paths.get(dir);
                                ApplicationManager.getApplication().invokeLater(() -> {
                                    try {
                                        String directoryTree;
                                        if (options != null) {
                                            Type listType = new TypeToken<ArrayList<String>>(){}.getType();
                                            List<String> ignoreDirs = gson.fromJson(
                                                    options.getAsJsonArray("ignoreDirs"),
                                                    listType
                                            );
                                            List<String> ignoreFileTypes = gson.fromJson(
                                                    options.getAsJsonArray("ignoreFileTypes"),
                                                    listType
                                            );
                                            directoryTree = fileManager.getDirectoryTree(dirPath, prefix, ignoreDirs, ignoreFileTypes);
                                        } else {
                                            directoryTree = fileManager.getDirectoryTree(dirPath, prefix, null, null);
                                        }
                                        sendMessageResult(null, Command.query_directoryTree, commandId, directoryTree);
                                    } catch (IOException e) {
                                        sendMessageResult(null, Command.query_directoryTree, commandId, "");
                                        System.out.println("获取文件目录树失败，" + e.getMessage());
                                    }
                                });
                            }
                        }
                    }

                    case Command.user_login -> {
                        LoginService loginService = LoginService.getInstance();
                        loginService.login();
                    }
                    case Command.user_logout -> {
                        LoginService loginService = LoginService.getInstance();
                        loginService.logout();
                        loginService.logoutMessage(project);
                    }
                    case Command.user_onLoginSuccess -> {
                        if (payload != null && !payload.isEmpty()) {
                            var accessToken = payload.get("accessToken") != null ? payload.get("accessToken").getAsString() : null;
                            var accountObject = payload.get("account") != null ? payload.get("account").getAsJsonObject() : null;
                            if (accessToken != null && accountObject != null && !accountObject.isEmpty()) {
                                Account account = gson.fromJson(accountObject, Account.class);
                                LoginService loginService = LoginService.getInstance();
                                loginService.loginSuccess(account, accessToken, project);
                            }
                        }
                    }
                    case Command.user_onLogoutSuccess -> {

                    }
                    case Command.user_onLoginExpired -> {

                    }

                    case Command.git_getBranchName -> {
                        String directory = project.getBasePath();
                        sendMessageResult(null, Command.git_getBranchName, commandId, GitInfoUtils.getBranch(directory));
                    }
                    case Command.git_getRepoName -> {
                        String directory = project.getBasePath();
                        sendMessageResult(null, Command.git_getRepoName, commandId, GitInfoUtils.getRepoName(directory));
                    }
                    case Command.git_getDiff -> {
                        String[] workspaceDirectories = pluginCoreService.getProcessClient().workspaceDirectories();
                        sendMessageResult(null, Command.git_getDiff, commandId, GitInfoUtils.getDiff(workspaceDirectories));
                    }
                    case Command.git_getLastCommitMessage -> {
                        GitRepositoryService gitRepositoryService = GitRepositoryService.getInstance(project);
                        sendMessageResult(null, Command.git_getLastCommitMessage, commandId, gitRepositoryService.getLastCommitMessage());
                    }

                    default -> System.out.println("未知的command: " + command);
                }
            } catch (Exception error) {
                System.out.println("无法处理该命令" + command + ": " + error);
            }
            return null;
        });

        // 监听页面加载完成事件
        browser.getJBCefClient().addLoadHandler(new CefLoadHandlerAdapter() {
            @Override
            public void onLoadingStateChange(CefBrowser browser, boolean isLoading, boolean canGoBack, boolean canGoForward) {
                if (!isLoading) {
                    // 页面完成加载
                    executeJavaScript(browser, myJSQueryOpenInBrowser);
                    injectJavaScript(pluginCoreService, browser);

                }
            }
        }, browser.getCefBrowser());

    }

    public JComponent getComponent() {
        return this.browser.getComponent();
    }

    /**
     * 构造消息体
     *
     * @param command 命令
     * @param payload 负载数据
     */
    private void sendMessageResult(String source, String command, String commandId, Object payload) {
        if (source == null) {
            MessageResult<Object> messageResult = new MessageResult<>(true, 0, "成功", payload);
            JsonObject mr = gson.toJsonTree(messageResult).getAsJsonObject();
            sendToWebview(null, command, commandId, mr);
        } else {
            JsonObject mr = gson.toJsonTree(payload).getAsJsonObject();
            sendToWebview(source, command, commandId, mr);
        }
    }

    /**
     * 发送命令到Webview
     * 此方法负责将指定的命令封装成GuiCommand对象，并根据当前是否已挂载Webview来决定是立即发送还是排队等待发送
     *
     * @param command 具体的命令枚举，表示要执行的操作类型
     * @param payload 命令的负载数据，通常包含执行命令所需的信息
     */
    public void sendToWebview(String source, String command, String commandId, JsonObject payload) {
        // 定义命令来源字符串常量，用于标识命令的发出者
        if (source == null) {
            source = "Jetbrains";
        }

        // 生成唯一的命令ID，用于后续可能的命令追踪或调试
        if (commandId == null || commandId.isBlank()) {
            commandId = UUID.randomUUID().toString();
        }

        // 创建GuiCommand对象，封装了发送到Webview的所有命令信息
        GuiCommand guiCommand = new GuiCommand(source, commandId, command, payload);

        // 检查Webview是否已挂载，决定当前是否可以发送命令
        if (guiMounted) {
            // 如果Webview已挂载，直接发送命令
            sendToWebview(guiCommand);
        } else {
            // 如果Webview未挂载，则将命令添加到队列中，等待后续发送
            pluginCoreService.addCommandQueue(guiCommand);
        }
    }

    /**
     * 将Gui命令发送到Webview执行
     * 此方法主要用于在Webview中执行JavaScript代码，以响应Gui命令
     * 它首先将Gui命令对象转换为JSON字符串，然后将其包装为JavaScript代码片段，最后在Webview中执行这些代码
     *
     * @param guiCommand 要发送到Webview执行的Gui命令对象
     */
    private void sendToWebview(GuiCommand guiCommand) {
        // 将Gui命令对象转换为JSON格式的字符串，并构建JavaScript代码
        var jsCode = buildJavaScript(gson.toJson(guiCommand));
        System.out.println("sendToWebview: " + jsCode);
        try {
            // 异步处理，确保在Swing组件上安全执行JavaScript
            SwingUtilities.invokeLater(() -> {
                // 获取当前的CefBrowser实例
                var cefBrowser = this.browser.getCefBrowser();
                // 在Webview中执行JavaScript代码
                cefBrowser.executeJavaScript(jsCode, cefBrowser.getURL(), 0);
            });
        } catch (Exception e) {
            // 处理异常情况，例如Webview尚未初始化
            System.out.println("Webview 尚未初始化: " + e);
        }
    }

    private String buildJavaScript(String jsonData) {
        return "window.postMessage(" + jsonData + ", \"*\");";
    }

    /**
     * 执行JavaScript代码
     *
     * @param browser                CefBrowser对象，表示浏览器实例
     * @param myJSQueryOpenInBrowser JBCefJSQuery对象，表示在浏览器中注入的JS查询对象
     */
    private void executeJavaScript(CefBrowser browser, JBCefJSQuery myJSQueryOpenInBrowser) {
        // 创建一个JavaScript代码字符串，用于在浏览器中执行
        var script = """
                window.postIntellijMessage = function(message) {
                    const msg = JSON.stringify(message);
                    %s
                }
                """.formatted(myJSQueryOpenInBrowser.inject("msg"));

        // 执行JavaScript代码，将script作为参数传递给浏览器实例
        browser.executeJavaScript(script, browser.getURL(), 0);
    }

    private void injectJavaScript(PluginCoreService pluginCoreService, CefBrowser browser) {
        String[] workspacePaths = pluginCoreService.getWorkspacePaths();
        String jsonWorkspacePaths = gson.toJson(workspacePaths);
        // 注入JavaScript代码到Webview中，以便与插件进行交互
        String script = """
                window.workspacePaths = %s;
                """.formatted(jsonWorkspacePaths);
        // 执行JavaScript代码，将script作为参数传递给浏览器实例
        browser.executeJavaScript(script, browser.getURL(), 0);
    }

}
