package cn.chinaunicom.paas.codeassistant.intellijextension.base;

/**
 * <AUTHOR>
 * @Date 2025/1/8 15:29
 **/

public class Command {

    /********************************日志**************************************************/
    public static final String log_info = "log/info";
    public static final String log_debug = "log/debug";
    public static final String log_warn = "log/warn";
    public static final String log_error = "log/error";

    /********************************会话**************************************************/
    public static final String history_load = "history/load";
    public static final String history_list = "history/list";
    public static final String history_save = "history/save";
    public static final String history_delete = "history/delete";
    public static final String history_clear = "history/clear";

    /********************************文件**************************************************/
    public static final String file_create = "file/create";
    public static final String file_open= "file/open";
    public static final String file_edit = "file/edit";
    public static final String file_delete = "file/delete";
    public static final String file_download = "file/download";
    public static final String file_readFile = "file/readFile";   //读取文件内容
    public static final String file_exists = "file/exists";   //文件是否存在
    public static final String file_readDirectory = "file/readDirectory";  //列出指定目录中的所有文件和文件夹

    /********************************配置**************************************************/
    public static final String config_get = "config/get";
    public static final String config_getAll = "config/getAll";
    public static final String config_update = "config/update";
    public static final String config_reload = "config/reload";

    /********************************IDE功能**************************************************/
    public static final String feature_openSettings = "feature/openSettings";  //打开配置面板
    public static final String feature_openHelp = "feature/openHelp";  //打开帮助文档
    public static final String feature_openUrl = "feature/openUrl";   //打开 url 链接
    public static final String feature_insertCode = "feature/insertCode";   //插入代码，若 codeInfo 不存在，插入到当前光标处
    public static final String feature_showDiff = "feature/showDiff";   //展示代码 Diff 信息
    public static final String feature_createTerminal = "feature/createTerminal";   //新建终端
    public static final String feature_insertToTerminal = "feature/insertToTerminal";   //插入内容到终端
    public static final String feature_showToast = "feature/showToast";   //显示 toast 通知窗口
    public static final String feature_cancelEditorSelection = "feature/cancelEditorSelection";   //取消编辑器选中
    public static final String feature_getErrors = "feature/getErrors";   //获取错误信息

    /********************************主题**************************************************/
    public static final String theme_onThemeChange = "theme/onThemeChange";

    /********************************事件**************************************************/
    public static final String event_onWebviewVisibleChange = "event/onWebviewVisibleChange";   //webview 显示隐藏
    public static final String event_onTextEditorSelectionChange = "event/onTextEditorSelectionChange";   //选中文件内容变化
    public static final String event_onServerTimeDiffChange = "event/onServerTimeDiffChange";   //服务器同步时间差变化
    public static final String event_onCancelEditorSelection = "event/onCancelEditorSelection";   //取消编辑器内容选中事件
    public static final String event_onAddContext = "event/onAddContext";   //添加代码上下文到 GUI(待定)
    public static final String event_onAddIDEContext = "event/onAddIDEContext";   //添加上下文到 IDE

    /********************************生命周期**************************************************/
    public static final String lifecycle_onGuiMounted = "lifecycle/onGuiMounted";   //GuiMounted 事件
    public static final String lifecycle_onCoreInitialed = "lifecycle/onCoreInitialed";   //core 初始化事件 事件

    /********************************指令**************************************************/
    public static final String directive_codeExplanation = "directive/codeExplanation";   //代码解释指令
    public static final String directive_functionComments = "directive/functionComments";   //方法注释
    public static final String directive_codeComments = "directive/codeComments";   //行间注释
    public static final String directive_codeOptimize = "directive/codeOptimize";   //代码优化
    public static final String directive_generateUintTest = "directive/generateUintTest";   //生成单测
    public static final String directive_codeTranslate = "directive/codeTranslate";   //生成单测
    public static final String directive_securityCheck = "directive/securityCheck";   //安全预审查
    public static final String directive_splitFunction = "directive/splitFunction";   //函数拆分
    public static final String directive_addLog = "directive/addLog";   //添加日志

    /********************************信息查询**************************************************/
    public static final String query_ideInfo = "query/ideInfo";   //获取 IDE 信息
    public static final String query_workspaceInfo = "query/workspaceInfo";   //获取工作空间信息（待定）
    public static final String query_workspaceDirs = "query/workspaceDirs";   //获取工作区目录列表
    public static final String query_pathSep = "query/pathSep";   //查询路径分隔符
    public static final String query_openFiles = "query/openFiles";   //获取当前打开的文件
    public static final String query_currentFile = "query/currentFile";   //获取当前文件信息
    public static final String query_lastModified = "query/lastModified";   //获取最近修改的文件信息
    public static final String query_listFolders = "query/listFolders";   //获取文件夹列表
    public static final String query_gitInfo = "query/gitInfo";   //获取 git 信息
    public static final String query_gotoDefinition = "query/gotoDefinition";   //获取 引用文件定义 信息
    public static final String query_readRangeInFile = "query/readRangeInFile";   //获取 文件内容
    public static final String query_directoryTree = "query/directoryTree";   //获取文件目录树

    /********************************Git**************************************************/
    public static final String git_getBranchName = "git/getBranchName";   //获取分支名称
    public static final String git_getRepoName = "git/getRepoName";   //获取代码库名称
    public static final String git_getDiff = "git/getDiff";   //获取 diff 信息
    public static final String git_getLastCommitMessage = "git/getLastCommitMessage";   //获取最后一次提交的 message
    public static final String git_onBranchChange = "git/onBranchChange";   //分支切换事件

    /********************************用户操作**************************************************/
    public static final String user_login = "user/login";
    public static final String user_logout = "user/logout";
    public static final String user_onLoginSuccess = "user/onLoginSuccess";
    public static final String user_onLoginFail = "user/onLoginFail";
    public static final String user_onLogoutSuccess = "user/onLogoutSuccess";
    public static final String user_onLoginExpired = "user/onLoginExpired";

    /********************************数据收集分析**************************************************/
    public static final String analytics_capture = "analytics/capture";     //收集埋点数据

    /********************************信息同步**************************************************/
    public static final String sync_globalInfo = "sync/globalInfo";     //同步全局信息

    /********************************index**************************************************/
    public static final String index_progress = "index/progress";       //文件索引进度
    public static final String index_setPaused = "index/setPaused";       //暂停、继续索引
    public static final String index_forceReIndex = "index/forceReIndex";       //重新索引
    public static final String index_clearIndex = "index/clearIndex";       //清除索引

    /********************************context**************************************************/
    public static final String context_getContextItems = "context/getContextItems";       //gui 召回知识库相关信息
    public static final String context_loadContextItems = "context/loadContextItems";       //gui 加载知识库相关上下文; 比如当前文件夹列表、文件列表等
    public static final String context_loadSubmenuItems = "context/loadSubmenuItems";       //gui 加载知识库相关上下文; 比如当前文件夹列表、文件列表等

    /********************************代码补全**************************************************/
    public static final String autocomplete_complete = "autocomplete/complete";     //代码补全

    /********************************MCP**************************************************/
    public static final String mcp_list = "mcp/list";     //mcp 服务列表
    public static final String mcp_callTool = "mcp/callTool";     //mcp 工具调用
    public static final String mcp_togglerServerDisabled = "mcp/togglerServerDisabled";     //mcp 服务开关
    public static final String mcp_deleteServer = "mcp/deleteServer";     //mcp 服务开关
    public static final String mcp_restartServer = "mcp/restartServer";     //重启mcp 服务
    public static final String mcp_addLocalServer = "mcp/addLocalServer";     //添加stdio 本地服务
    public static final String mcp_addRemoteServer = "mcp/addRemoteServer";     //添加sse 远程服务
    public static final String mcp_editConfig = "mcp/editConfig";     //编辑本地mcp-config.json
    public static final String mcp_updateConfig = "mcp/updateConfig";     //更新mcp服务配置
    public static final String mcp_getConfig = "mcp/getConfig";     //获取mcp服务配置

    /********************************MCP**************************************************/
    public static final String aiRules_open = "aiRules/open";     //打开ai 规则
    public static final String aiRules_insert = "aiRules/insert";     //插入ai 规则
    public static final String aiRules_get = "aiRules/get";     //获取ai 规则
}


