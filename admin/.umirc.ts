import { defineConfig } from '@umijs/max';
import routes from './src/routes'
import { layout } from '@/app';

export default defineConfig({
  title: '联通云智能编程助手',
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: {
    title: '联通云智能编程助手',
  },
  routes,
  npmClient: 'pnpm',
  publicPath: '/admin/',
  base: '/admin/',
  proxy: {
    '/console/api': {
      target: 'http://10.64.46.141:25001',
      changeOrigin: true
    }
  }
});

