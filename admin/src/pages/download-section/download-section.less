.download-section {
  margin: 0 auto;
  text-align: center;
  width: 1200px;

  &-title {
    font-size: 36px;
  }

  .tab-buttons {
    margin-top: 36px;
    display: flex;
    justify-content: center;

    .tab-button {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 16px;
      width: 240px;
      height: 48px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 20px;
      color: #016efc;
      cursor: pointer;
      border: double 2px transparent;
      background-image: linear-gradient(white, white),
        linear-gradient(315deg, rgba(0, 190, 247, 1), rgba(0, 118, 232, 1));
      background-origin: border-box;
      background-clip: content-box, border-box;

      &:hover {
        background-image: linear-gradient(#e6f5ff, #e6f5ff),
          linear-gradient(315deg, rgba(0, 190, 247, 1), rgba(0, 118, 232, 1));
      }

      &.is-active {
        color: #fff;
        background: linear-gradient(311deg, #14c3f9 0%, #176bdf 100%);
        border-radius: 8px;
        border: none;
      }
    }
  }
}

.download-box {
  margin-bottom: 80px;
  margin-top: 54px;
  display: flex;
  background: radial-gradient(
    rgba(50, 194, 253, 0.15) 0%,
    rgba(255, 255, 255, 0.24) 60%
  );
  box-shadow: 0px 6px 20px 12px rgba(0, 0, 0, 0.06);
  border-radius: 8px;

  &-left {
    flex-shrink: 0;
    width: 454px;
    padding-top: 110px;
  }

  &-right {
    flex: 1;
    background: linear-gradient(315deg, #1d99f3 0%, #016efc 100%);
    border-radius: 8px;
    padding: 42px 50px 40px 26px;
  }

  .ide-logo {
    width: 160px;
    height: 160px;
  }

  .ide-name {
    margin-top: 36px;
    font-weight: 600;
    font-size: 24px;
    color: rgba(0, 0, 0, 0.9);
    line-height: 24px;
  }

  .download-button {
    margin: 42px auto 0;
    width: 286px;
    height: 48px;
    background: linear-gradient(315deg, #6cc7f8 0%, #2b81ff 66%, #1d99f3 100%);
    border-radius: 2px;
    font-weight: 600;
    font-size: 16px;
    line-height: 48px;
    color: #fff;
    cursor: pointer;
  }

  .other-version {
    color: #2b81ff;
  }
}

.install-guide {
  text-align: left;

  &-step {
    position: relative;
    padding-left: 66px;
    padding-bottom: 24px;

    &-linen {
      position: absolute;
      height: 100%;
      left: 0;
      top: 0;
      z-index: 1;
    }

    &-content {
      color: #fff;
      font-size: 14px;
      line-height: 22px;
    }

    .step-icon {
      width: 26px;
      height: 26px;
      border-radius: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #176bdf;

      &.icon-1 {
        background: #61ffed;
      }

      &.icon-2 {
        background: #bbfff7;
      }

      &.icon-3 {
        background: #fff;
      }

      &.icon-4 {
        background: #fff;
      }
    }

    .step-line {
      position: absolute;
      margin-top: 4px;
      margin-left: 12px;
      height: calc(100% - 34px);
      width: 3px;
      border-radius: 10px;
      background: linear-gradient(
        315deg,
        rgba(80, 255, 222, 0.3),
        rgba(0, 249, 255, 1)
      );
    }
  }

  &-image {
    margin-top: 10px;
    width: 100%;
  }
}
