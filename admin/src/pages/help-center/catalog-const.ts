
type CatalogEntry = [number, string];
export const CATALOG_ORIGIN_LIST: CatalogEntry[] = [
  [1, '1 产品动态'],
  [11, '1.1 更新日志'],
  [111, '1.1.1 Visual Studio Code 插件'],
  [112, '1.1.2 JetBrains IDEs 插件'],
  [2, '2 产品介绍'],
  [21, '2.1 产品定义'],
  [22, '2.2 产品优势'],
  [23, '2.3 产品功能'],
  [24, '2.4 应用场景'],
  [25, '2.5 基本概念'],
  [26, '2.6 使用限制'],
  [3, '3 快速入门'],
  [31, '3.1 插件快速上手'],
  [4, '4 用户指南'],
  [41, '4.1 实时代码续写'],
  [411, '4.1.1 块级代码续写'],
  [412, '4.1.2 行级代码续写'],
  [42, '4.2 智能代码生成'],
  [421, '4.2.1 行间注释生成'],
  [422, '4.2.2 函数注释生成'],
  [423, '4.2.3 单元测试生成'],
  [424, '4.2.4 代码优化生成'],
  [43, '4.3 智能问答'],
  [431, '4.3.1 自由智能问答'],
  [44, '4.4 个性化配置'],
  [441, '4.4.1 代码续写配置'],
  [442, '4.4.2 单测生成配置'],
]

export interface ICatalog {
  title: string,
  id: string;
}

function switchCatologToMap(catalog: CatalogEntry[]): { [key: string]: ICatalog} {
  const _catalogMap = new Map<string, ICatalog>()
  catalog.forEach(([key, val]) => {
    _catalogMap.set(key.toString(), {
      title: val,
      id: switchStrToID(val)
    })
  })

  return Object.fromEntries(_catalogMap);
}

function switchStrToID(title: string) {
  return title
  .replace(/\s+/g, '-')
  .replace(/\.+/g, '');
}

export const CATALOG_MAP = switchCatologToMap(CATALOG_ORIGIN_LIST)

function getImageUrl(): { [key: string]: string } {
  const imgUrlMap: { [key: string]: string } = {};
  const urlPrefixList: [string, number][] = [
    ['31', 14],
    ['411', 2],
    ['421', 5],
    ['422', 1],
    ['423', 1],
    ['424', 1],
    ['431', 8],
    ['441', 1],
    ['442', 1],
  ];
  urlPrefixList.forEach(([prefix, total]) => {
    for (let i = 0; i < total; i++) {
      const id = `${prefix}-${i + 1}`
      imgUrlMap[id] = require(`@/assets/images/help-center/${id}.jpg`);
    }
  })

  return imgUrlMap;
}

export const IMAGE_URL_MAP = getImageUrl()
