.help-center-page {
  position: relative;
  max-width: 1160px;
  margin: auto;
  * {
    margin: 0;
    /* 整体滚动条样式 */
    ::-webkit-scrollbar {
      width: 8px; /* 垂直滚动条宽度 */
      height: 8px; /* 水平滚动条高度 */
    }

    /* 滚动条轨道（背景）样式 */
    ::-webkit-scrollbar-track {
      background: #f4f4f4; /* 轨道背景色 */
      border-radius: 5px; /* 圆角 */
    }

    /* 滚动条滑块（滚动块）样式 */
    ::-webkit-scrollbar-thumb {
      background: #b9b7b7; /* 滑块背景色 */
      border-radius: 5px; /* 圆角 */
      border: 2px solid #f4f4f4; /* 滑块边框，使滑块与轨道分离 */
    }

    /* 滑块悬停状态 */
    ::-webkit-scrollbar-thumb:hover {
      background: #888; /* 悬停时滑块背景色 */
    }

    /* 滚动条两端按钮样式 */
    ::-webkit-scrollbar-button {
      display: none; /* 隐藏滚动条按钮 */
    }
  }
  .affix-bottom {
    position: fixed;
    bottom: 60px;
    right: 20px;
    z-index: 10;
    visibility: hidden;
  }
  .affix-visible {
    visibility: visible;
  }
  h2 {
    padding: 18px 0 4px;
    line-height: 30px;
    font-size: 22px;
  }
  h3 {
    padding: 18px 0 4px;
    line-height: 28px;
    font-size: 20px;
  }
  h4 {
    padding: 18px 0 4px;
    line-height: 26px;
    font-size: 18px;
  }
  p {
    font-size: 14px;
    color: rgba(34, 34, 34, .9);
    line-height: 26px;
  }
  table {
    margin: 8px 0;
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    th {
      background: rgba(34, 34, 34, .08);
      padding: 12px;
      border: 1px solid rgba(34, 34, 34, .08);
      border-left: none;
      &:first-child {
        border-left: 1px solid rgba(34, 34, 34, .08);
        border-top-left-radius: 4px;
      }
    }
    tbody {
      line-height: 24px;
      td {
        min-width: 48px;
        font-size: 14px;
        line-height: 24px;
        color: rgba(34,34,34,.9);
        text-align: left;
        font-weight: 400;
        padding: 6px 10px;
        border: 1px solid rgba(34,34,34,.08);
        border-top: none;
        border-left: none;
        &:first-child  {
          border-left: 1px solid rgba(34,34,34,.08)
        }
      }
      tr:last-child td:first-child {
        border-bottom-left-radius: 4px
      }
      tr:last-child td:last-child {
        border-bottom-right-radius: 4px
      }
    }
  }
  img {
    display: block;
    max-width: 100%;
    border: 1px solid rgba(34, 34, 34, .04);
    border-radius: 4px;
    margin: 4px 0;
  }
  ol {
    padding-left: 28px;
  }
  li {
    line-height: 24px;
  }
  .content-wrapper {
    margin-right: 300px;
  }
  .nav {
    position: fixed;
    top: 60px;
    right: 0;
    width: 300px;
    height: calc(100vh - 60px);
    overflow: overlay;
    &-title {
      margin-bottom: 12px;
      font-size: 16px;
      color: #222;
      line-height: 26px;
      font-weight: 500;
      padding: 0 24px 0 20px;
    }
  }
  .menu-wrapper {
    height: calc(100% - 60px);
    overflow-y: auto;
    ul {
      list-style: none;
      padding: 0;
      li {
        padding: 4px 0 4px 20px;
        .link {
          color: rgba(34, 34, 34, .7);
          margin-left: 0;
          font-family: PingFangSC-Regular;
          font-size: 14px;
          text-align: left;
          line-height: 24px;
          font-weight: 400;
          word-wrap: break-word;
          word-break: normal;
          text-overflow: clip;
          white-space: inherit;
          -webkit-transition: color .2s ease;
          transition: color .2s ease;
        }
        &.level-2 .link {
          color: rgba(34, 34, 34, .9);
        }
        &.level-3 .link {
          padding-left: 16px;
        }
        &.level-4 .link {
          padding-left: 32px;
        }
        &.active .link {
          color: #2468f2;
          font-weight: 500;
        }
      }
    }
  }
}
