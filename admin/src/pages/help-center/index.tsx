import styles from './help-center.less'
import { CATALOG_MAP, CATALOG_ORIGIN_LIST, IMAGE_URL_MAP } from './catalog-const'
import { useEffect, useState } from 'react'
import cls from 'classnames'
import React from 'react';
import { Affix, Button } from 'antd';
import { VerticalAlignTopOutlined } from '@ant-design/icons'

const HelpCenter: React.FC = () => {
  const [activeSection, setActiveSection] = useState(0); // 当前导航位置

  useEffect(() => {
    const handleScroll = () => {
      let currentSection = 0;
      CATALOG_ORIGIN_LIST.forEach(([key]) => {
        const id = CATALOG_MAP[key.toString()].id
        const element = document.getElementById(id);
        if (element) {
          const sectionTop = element.offsetTop;
          const sectionHeight = element.offsetHeight;
          if (window.scrollY >= sectionTop - sectionHeight / 3) {
            currentSection = Number(id.split('-')[0]);
          }
        }
      });
      if (currentSection && currentSection !== activeSection) {
        setActiveSection(currentSection);
      }
    };

    // 添加滚动事件监听
    window.addEventListener('scroll', handleScroll);

    // 清除滚动事件监听
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [])

  return (
    <div className={styles['help-center-page']}>
      <Affix className={cls(styles['affix-bottom'], {[styles['affix-visible']]: activeSection > 1})}>
        <Button
          icon={<VerticalAlignTopOutlined />}
          title='scroll to top'
          onClick={() => window.scroll({ top: 0, left: 0, behavior: 'smooth' })}
         >

        </Button>
      </Affix>

      <nav className={styles['nav']}>
        <p className={styles['nav-title']}>本页目录</p>
        <div className={styles['menu-wrapper']}>
          <ul>
            {
              CATALOG_ORIGIN_LIST.map(([key, catalogTitle]) => (
                <li
                key={key}
                className={cls(styles[`level-${key.toString().length + 1}`], {[styles['active']]: activeSection === key})}
                onClick={() => setActiveSection(key)}
                >
                  <a href={`#${CATALOG_MAP[key.toString()].id}`} className={styles['link']}>{catalogTitle}</a>
                </li>
              ))
            }
          </ul>
        </div>
      </nav>

      <div className={styles['content-wrapper']}>
        <h2 id={CATALOG_MAP['1'].id}>{CATALOG_MAP['1'].title}</h2>
        <h3 id={CATALOG_MAP['11'].id}>{CATALOG_MAP['11'].title}</h3>
        <h4 id={CATALOG_MAP['111'].id}>{CATALOG_MAP['111'].title}</h4>
        <p>2024 年 10 月更新日志</p>
        <p>版本号：v1.0.0</p>
        <table>
          <thead>
            <tr>
              <th>发布日期</th>
              <th>功能名称</th>
              <th>功能描述</th>
              <th>相关文档</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>2024/10/18</td>
              <td>插件安装包</td>
              <td>适用 Visual Studio Code 的 v1.0.0版本 vsix 安装包</td>
              <td>3.1 章节</td>
            </tr>
            <tr>
              <td>2024/10/18</td>
              <td>实时代码续写</td>
              <td>在代码编写过程中实时根据当前上下文智能推荐续写代码</td>
              <td>4.1 章节</td>
            </tr>
            <tr>
              <td>2024/10/18</td>
              <td>智能代码生成</td>
              <td>智能生成函数的单元测试、代码注释和代码优化</td>
              <td>4.2 章节</td>
            </tr>
            <tr>
              <td>2024/10/18</td>
              <td>智能问答</td>
              <td>智能知识和代码问答，支持多轮对话、会话管理和多种结果采纳操作</td>
              <td>4.3 章节</td>
            </tr>
            <tr>
              <td>2024/10/18</td>
              <td>个性化配置</td>
              <td>个性化配置代码续写策略和单测代码所使用的框架</td>
              <td>4.4 章节</td>
            </tr>
          </tbody>
        </table>

        <h4 id={CATALOG_MAP['112'].id}>{CATALOG_MAP['112'].title}</h4>
        <p>2024 年 10 月更新日志</p>
        <p>版本号：v1.0.0</p>
        <table>
          <thead>
            <tr>
              <th>发布日期</th>
              <th>功能名称</th>
              <th>功能描述</th>
              <th>相关文档</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>2024/10/18</td>
              <td>插件安装包</td>
              <td>适用 JetBrains IDEs 的 v1.0.0 版本 zip 安装包</td>
              <td>3.1 章节</td>
            </tr>
            <tr>
              <td>2024/10/18</td>
              <td>实时代码续写</td>
              <td>在代码编写过程中实时根据当前上下文智能推荐续写代码</td>
              <td>4.1 章节</td>
            </tr>
            <tr>
              <td>2024/10/18</td>
              <td>智能代码生成</td>
              <td>智能生成函数的单元测试、代码注释和代码优化</td>
              <td>4.2 章节</td>
            </tr>
            <tr>
              <td>2024/10/18</td>
              <td>智能问答</td>
              <td>智能知识和代码问答，支持多轮对话、会话管理和多种结果采纳操作</td>
              <td>4.3 章节</td>
            </tr>
            <tr>
              <td>2024/10/18</td>
              <td>个性化配置</td>
              <td>个性化配置代码续写策略和单测代码所使用的框架</td>
              <td>4.4 章节</td>
            </tr>
          </tbody>
        </table>

        <h2 id={CATALOG_MAP['2'].id}>{CATALOG_MAP['2'].title}</h2>
        <h3 id={CATALOG_MAP['21'].id}>{CATALOG_MAP['21'].title}</h3>
        <p>联通云智能编程助手是一款基于联通元景大模型的智能编程辅助工具。利用人工智能技术为程序员提供代码自动生成、补全、错误检测与修复、优化等功能，同时也是一个知识渊博的编程伙伴，能解答研发过程中的各类问题，可应用于软件开发、代码审查等场景。其优势在于沉浸式的视觉和交互体验，极致的应答速度以及灵活的个性化定制能力，让软件开发人员更加专注技术设计、高效高质量的完成编码工作。</p>

        <h3 id={CATALOG_MAP['22'].id}>{CATALOG_MAP['22'].title}</h3>
        <p>智能上下文感知能力，精准贴合场景</p>
        <p>插件端与模型层协同优化代码跨文件上下文感知能力，使得生成的代码能高度契合当前代码库的场景，为生成代码的准确性和采纳率提供有力支撑。</p>
        <p>适配主流编辑器，提供契合开发者习惯的沉浸体验</p>
        <p>定制适配多种 IDE 的原生主题与交互模式，让开发者尽享 IDE 的原生视觉效果与交互体验，完美符合开发者的使用习惯，带来流畅的开发感受。</p>
        <p>灵活的企业定制和部署方案</p>
        <p>提供企业标准版、企业专属版等丰富多样的面向企业客户的方案，同时还可提供企业个性化方案，给予企业灵活选择的空间，加速企业内智能研发的规模化落地进程。</p>

        <h3 id={CATALOG_MAP['23'].id}>{CATALOG_MAP['23'].title}</h3>
        <table>
          <thead>
            <tr>
              <th>模块</th>
              <th>功能点</th>
              <th>功能描述</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td rowSpan={2}>插件安装</td>
              <td>VS Code</td>
              <td rowSpan={2}>适配主流编辑器，提供更加原生的视觉和交互体验，支持 MacOS 和 Windows 系统</td>
            </tr>
            <tr>
              <td>JetBrains IDEs</td>
            </tr>
            <tr>
              <td rowSpan={2}>实时代码续写</td>
              <td>行级代码续写</td>
              <td>支持在代码编写过程中实时根据当前编程语言和当前的上下文智能推荐当前行的续写代码</td>
            </tr>
            <tr>
              <td>块级代码续写</td>
              <td>支持在代码编写过程中实时根据当前编程语言和当前文件上下文智能推荐块级多行代码</td>
            </tr>
            <tr>
              <td rowSpan={3}>智能代码生成</td>
              <td>单元测试生成</td>
              <td>支持生成各种编程语言主流单测框架的单元测试代码</td>
            </tr>
            <tr>
              <td>代码注释生成</td>
              <td>支持生成函数注释和函数内的行间注释</td>
            </tr>
            <tr>
              <td>代码优化生成</td>
              <td>支持对函数生成修复或优化代码及建议</td>
            </tr>
            <tr>
              <td rowSpan={2}>智能问答</td>
              <td>自由智能问答</td>
              <td>支持多轮连续问答和会话历史清除，可展示文本、链接、代码等应答内容，并可进行复制、采纳等操作</td>
            </tr>
            <tr>
              <td>指令问答</td>
              <td>在提问框中添加特定指令如：代码解释、生成单测等，然后框选函数或方法进行提问</td>
            </tr>
            <tr>
              <td rowSpan={2}>个性化配置</td>
              <td>代码续写配置</td>
              <td>支持开启/关闭代码续写和指定行级/块级优先</td>
            </tr>
            <tr>
              <td>单测生成配置</td>
              <td>支持按语言指定生成的单测代码所使用的框架</td>
            </tr>
          </tbody>
        </table>

        <h3 id={CATALOG_MAP['24'].id}>{CATALOG_MAP['24'].title}</h3>
        <p>代码智能生成</p>
        <p>经过海量优质开源代码数据的精心训练，该工具能够依据当前代码文件以及跨文件的上下文，为你高效生成行级或函数级代码。同时，还能快速生成单元测试，为代码质量保驾护航。此外，它会贴心地给出代码优化建议，让你的编码工作更加高质高效。沉浸于编码的心流之中，感受秒级生成速度，使你能够更加专注于技术设计，轻松完成编码任务。</p>
        <p>研发智能问答</p>
        <p>经过海量优质开源代码数据的精心训练，该工具能够依据当前代码文件以及跨文件的上下文，为你高效生成行级或函数级代码。同时，还能快速生成单元测试，为代码质量保驾护航。此外，它会贴心地给出代码优化建议，让你的编码工作更加高质高效。沉浸于编码的心流之中，感受秒级生成速度，使你能够更加专注于技术设计，轻松完成编码任务。</p>

        <h3 id={CATALOG_MAP['25'].id}>{CATALOG_MAP['25'].title}</h3>
        <table>
          <thead>
            <tr>
              <th>概念/名词</th>
              <th>说明</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>智能问答</td>
              <td>依托大模型技术提供可连续进行多轮对话的问答工具</td>
            </tr>
            <tr>
              <td>指令提问</td>
              <td>基于代码进行问答场景下，将一些常见问题内置为指令，提高提问效率</td>
            </tr>
            <tr>
              <td>代码续写</td>
              <td>在代码编写过程中，实时根据前上下文智能推荐续写代码</td>
            </tr>
            <tr>
              <td>个性化配置</td>
              <td>存储在用户代码编辑器本地的个性化配置，方便根据个人使用习惯定制智能编程助手的功能和应答变现等</td>
            </tr>
          </tbody>
        </table>

        <h3 id={CATALOG_MAP['26'].id}>{CATALOG_MAP['26'].title}</h3>
        <table>
          <thead>
            <tr>
              <th>模块</th>
              <th>限制项</th>
              <th>约束与限制</th>
              <th>限制说明</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>智能问答</td>
              <td>提问内容长度</td>
              <td>10000 字符</td>
              <td>问题过长会影响智能助手的分析和应答效果，建议分步通过多轮对话进行提问</td>
            </tr>
            <tr>
              <td>智能指令</td>
              <td>函数代码长度</td>
              <td>2000 行</td>
              <td>函数过长会影响智能助手的分析和处理效果，建议分段分开提问</td>
            </tr>
          </tbody>
        </table>

        <h2 id={CATALOG_MAP['3'].id}>{CATALOG_MAP['3'].title}</h2>
        <h3 id={CATALOG_MAP['31'].id}>{CATALOG_MAP['31'].title}</h3>
        <p>前置准备</p>
        <ol>
          <li>电脑操作系统版本检查和升级，Windows 7 及以上、MacOS Monterey 及以上版本。</li>
          <li>
          代码编辑器版本检查和升级
            <ul>
              <li>Visual Studio Code 1.75.0 及以上版本</li>
              <li>JetBrains IDEs（如：WebStorm） 2023.01 及以上版本</li>
            </ul>
          </li>
        </ol>
        <p>下载和安装</p>
        <ol>
          <li>
            联通云智能编程助手目前仅支持通过安装代码编辑器插件的形式进行使用。支持的代码编辑器有：
              <ul>
                <li>Visual Studio Code</li>
                <li>JetBrains IDEs（如：IntelliJ IDEA、PyCharm、GoLand、WebStorm 等）</li>
              </ul>
          </li>
          <li>
            联通云智能编程助手目前仅支持「手动下载安装包」的方式进行安装。访问<a href='/admin/download'>「联通云智能编程助手官网」</a>获取安装包
            <img src={IMAGE_URL_MAP['31-1']} />
          </li>
          <li>Visual Studio Code 手动安装
            <ol type='I'>
              <li>下载并安装 Visual Studio Code 1.75.0 及以上版本。</li>
              <li>
                打开 Visual Studio Code 后，单击扩展，找到更多按钮，在下拉菜单中单击从 VSIX 安装，选择从统一研发平台上下载的 VSIX 文件，点击确认进行安装。
                <img src={IMAGE_URL_MAP['31-2']} />
              </li>
              <li>
                安装完成后可看到成功安装提示，左侧工具栏出现智能编程助手插件入口。
                <img src={IMAGE_URL_MAP['31-3']} />
                </li>
            </ol>
          </li>
          <li>JetBrains IDEs 手动安装
            <ol type='I'>
              <li>下载并安装 JetBrains IDEs 2023.01 及以上版本，以在 WebStorm 中安装为例。</li>
              <li>
                打开 WebStorm 后，在设置下拉菜单中在设置下拉菜单中单击插件，打开插件弹窗。
                <img src={IMAGE_URL_MAP['31-4']} />
              </li>
              <li>
                在插件弹窗的设置下拉菜单中，单击从磁盘安装插件，选择下载的 zip 文件安装。
                <img src={IMAGE_URL_MAP['31-5']} />
              </li>
              <li>
                安装完成后，点击确定关闭插件弹窗，在 WebStorm 右侧插件扩展区即可看到刚刚安装好的插件。
                <img src={IMAGE_URL_MAP['31-6']} />
                </li>
            </ol>
          </li>
        </ol>
        <p>登录</p>
        <p>登录账号为公司邮箱的前缀，输入后点击下一步，第一次登录需要先设置个人密码。</p>
        <img src={IMAGE_URL_MAP['31-7']} />
        <img src={IMAGE_URL_MAP['31-8']} />
        <p>版本更新</p>
        <p>若插件有新版本更新，会在插件菜单栏展示发现新版本提示，点击可进入统一研发平台智能编程助手产品模块，查看并下载安装。</p>
        <img src={IMAGE_URL_MAP['31-9']} />
        <p>若希望安装历史其他版本，可点击其他版本前往查看并下载安装。</p>
        <img src={IMAGE_URL_MAP['31-10']} />
        <p>在安装新版本之前，需要先卸载当前版本插件。</p>
        <ul>
          <li>
            Visual Studio Code：单击扩展，在已安装的插件中找到联通云智能编程助手，点击设置按钮，在下拉菜单中单击卸载。卸载成功后点击需要更新加载按钮重新加载插件区，再重新安装新的版本即可。
            <img src={IMAGE_URL_MAP['31-11']} />
            <img src={IMAGE_URL_MAP['31-12']} />
            </li>
          <li>
            JetBrains IDEs：打开 WebStorm 后，在设置下拉菜单中单击插件，打开插件弹窗；在插件弹窗中的已安装插件中找到联通云智能编程助手，在右键菜单中单击卸载。卸载成功后 WebStorm 会提示重启 IDE，点击重启即可，之后便可再重新安装新版本的插件。
            <img src={IMAGE_URL_MAP['31-13']} />
            <img src={IMAGE_URL_MAP['31-14']} />
            </li>
        </ul>

        <h2 id={CATALOG_MAP['4'].id}>{CATALOG_MAP['4'].title}</h2>
        <h3 id={CATALOG_MAP['41'].id}>{CATALOG_MAP['41'].title}</h3>
        <h4 id={CATALOG_MAP['411'].id}>{CATALOG_MAP['411'].title}</h4>
        <p>功能概述</p>
        <p>块级多行代码续写与单行代码续写的使用场景非常相似，区别在于当前编写代码位置处于块级上下文或作用域中，如for 循环、if 判断等。</p>
        <p>前提条件</p>
        <ol>
          <li>插件设置中开启了实时代码续写</li>
          <li>编辑的代码文件为主流编程语言，特定编程语言（如 Go、Java、Python、JavaScript、C/C++ 、TypeScript）场景下推荐效果更优</li>
        </ol>
        <p>操作指南</p>
        <p>在代码编辑器的代码编辑区进行代码编写时，在代码中的块级上下文或作用域稍作停顿时，智能编程助手便会根据当前代码文件的上下文，智能生成多行代码推荐。</p>
        <img src={IMAGE_URL_MAP['411-1']} />
        <p>如果觉得推荐内容不错，即可使用 tab 键采纳，否则可以使用 ESC 键取消推荐。</p>
        <img src={IMAGE_URL_MAP['411-2']} />

        <h4 id={CATALOG_MAP['412'].id}>{CATALOG_MAP['412'].title}</h4>
        <p>功能概述</p>
        <p>单行代码推荐是智能编程助手在辅助编程场景下最直接、最高效的产品功能。</p>
        <p>前提条件</p>
        <ol>
          <li>插件设置中开启了实时代码续写</li>
          <li>编辑的代码文件为主流编程语言，特定编程语言（如 Go、Java、Python、JavaScript、C/C++ 、TypeScript）场景下推荐效果更优</li>
        </ol>
        <p>操作指南</p>
        <p>在代码编辑器的代码编辑区进行代码编写时，在编写代码的间隙只需稍作等待，智能编程助手便会根据当前行已键入代码和相关代码文件的上下文，智能生成当前行的后续代码推荐。</p>

        <h3 id={CATALOG_MAP['42'].id}>{CATALOG_MAP['42'].title}</h3>
        <h4 id={CATALOG_MAP['421'].id}>{CATALOG_MAP['421'].title}</h4>
        <p>功能概述</p>
        <p>通过理解一个函数（方法）的内容，在该函数（方法）内部按行或段落生成行间注释。</p>
        <p>前提条件</p>
        <p>框选的函数（方法）为主流编程语言。</p>
        <p>操作指南</p>
        <p>可通过如下两种方式触发函数或方法的行间注释生成，其他智能指令也同理。</p>
        <ul>
          <li>
            右键菜单触发
            <p>在编辑器中框选希望进行提问的函数或方法，右键菜单中找到联通云智能编程助手，选择行间注释指令，点击触发。</p>
            <img src={IMAGE_URL_MAP['421-1']} />
            <p>插件将被自动唤起，并使用框选的代码和对应指令进行提问，并开始应答。</p>
            <img src={IMAGE_URL_MAP['421-2']} />
          </li>
          <li>指令提问触发
            <p>通过插件入口或快捷键唤起联通云智能编程助手插件，在提问框中输入/或者点击指令按钮，即可弹出指令菜单，选择行间注释指令，会看到提问框中出现框选代码提示。</p>
            <img src={IMAGE_URL_MAP['421-3']} />
            <p>在编辑器中框选合适的函数或方法，再点击提问框中的发送按钮，即可完成指令提问，并开始应答。</p>
            <img src={IMAGE_URL_MAP['421-4']} />
            <img src={IMAGE_URL_MAP['421-5']} />
            <p>更多说明：生成函数内的行间注释，函数代码过长（如 2000 行以上）时，可能无法生成，其他函数相关的智能指令也有相同限制。</p>
          </li>
        </ul>

        <h4 id={CATALOG_MAP['422'].id}>{CATALOG_MAP['422'].title}</h4>
        <p>功能概述</p>
        <p>通过理解一个函数（方法）的内容，为该函数（方法）的上方生成整体函数（方法）注释。</p>
        <p>前提条件</p>
        <p>框选的函数（方法）为主流编程语言。</p>
        <p>操作指南</p>
        <p>在编辑器中框选希望进行提问的函数或方法，右键菜单中找到联通云智能编程助手，选择函数注释指令，点击触发。</p>
        <img src={IMAGE_URL_MAP['422-1']} />

        <h4 id={CATALOG_MAP['423'].id}>{CATALOG_MAP['423'].title}</h4>
        <p>功能概述</p>
        <p>使用各种编程语言的主流单测框架，智能生成函数的单元测试代码。</p>
        <p>前提条件</p>
        <p>框选的函数（方法）为主流编程语言。</p>
        <p>操作指南</p>
        <p>在编辑器中框选希望进行提问的函数或方法，右键菜单中找到联通云智能编程助手，选择生成单测指令，点击触发。</p>
        <img src={IMAGE_URL_MAP['423-1']} />

        <h4 id={CATALOG_MAP['424'].id}>{CATALOG_MAP['424'].title}</h4>
        <p>功能概述</p>
        <p>智能分析函数，并修复代码中的缺陷或优化代码实现。</p>
        <p>前提条件</p>
        <p>框选的函数（方法）为主流编程语言。</p>
        <p>操作指南</p>
        <p>在编辑器中框选希望进行提问的函数或方法，右键菜单中找到联通云智能编程助手，选择代码优化指令，点击触发。</p>
        <img src={IMAGE_URL_MAP['424-1']} />

        <h3 id={CATALOG_MAP['43'].id}>{CATALOG_MAP['43'].title}</h3>
        <h4 id={CATALOG_MAP['431'].id}>{CATALOG_MAP['431'].title}</h4>
        <p>功能概述</p>
        <p>唤起智能编程助手插件问答窗口，直接输入问题并发问，智能编程助手会自动感知历史提问上下文，做出更好的应答。</p>
        <p>操作指南</p>
        <ol>
          <li>点击扩展工具栏中的插件入口，打开问答窗口，在提问框中键入问题并发送，智能编程助手便会立即为你应答。</li>
          <li>还可以通过快捷键唤起智能编程助手的问答窗口：
          <table>
            <thead>
              <tr>
                <th>功能</th>
                <th>系统</th>
                <th>客户端</th>
                <th>快捷键</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td rowSpan={4}>唤起插件</td>
                <td rowSpan={2}>MacOS</td>
                <td>VSCode</td>
                <td>⌘ + Y</td>
              </tr>
              <tr>
                <td>JetBrains IDEs</td>
                <td>⌘ + Shift + Y</td>
              </tr>
              <tr>
                <td rowSpan={2}>Windows</td>
                <td>VSCode</td>
                <td>Ctrl + Y</td>
              </tr>
              <tr>
                <td>JetBrains IDEs</td>
                <td>Ctrl + Shift + Y</td>
              </tr>
              <tr>
                <td rowSpan={2}>提问执行</td>
                <td>MacOS</td>
                <td>不区分</td>
                <td>⌘ + Enter</td>
              </tr>
              <tr>
                <td>Windows</td>
                <td>不区分</td>
                <td>Alt + Enter</td>
              </tr>
            </tbody>
          </table>
          </li>
          <li>应答的内容会逐行进行打印，应答的结果中可能会包含文字解答、代码输出、链接输出等格式，其中代码输出会进行语法解析和高亮。
            <img src={IMAGE_URL_MAP['431-1']} />
          </li>
          <li>如果对应答的内容不太满意，或者问题表述有所偏差，可点击停止应答或在提问框输入新的问题再次提问，上一个未完成的应答会立即停止，开始处理新的提问。
            <img src={IMAGE_URL_MAP['431-2']} />
          </li>
          <li>如果应答内容中的代码符合需求，可以通过代码块右上方的工具栏进行采纳，具体采纳方式有：
            <ul>
              <li>复制：将代码块中的代码复制至剪切板
                <img src={IMAGE_URL_MAP['431-3']} />
                <img src={IMAGE_URL_MAP['431-4']} />
              </li>
              <li>采纳：
                <ul>
                  <li>当有框选的函数（方法）时，如函数注释、行间注释、代码优化等指令操作，点击采纳按钮在框选的函数（方法）的原位替换代码块中的代码</li>
                  <li>当没有框选函数（方法）时，即为普通提问，点击采纳按钮会在当前光标所在位置插入代码块中的代码
                    <img src={IMAGE_URL_MAP['431-5']} />
                    <img src={IMAGE_URL_MAP['431-6']} />
                  </li>
                </ul>
              </li>
              <li>新建文件：
                <ul>
                  <li>Visual Studio Code：在临时暂存区创建新的代码文件，并将代码块中的代码写入新的文件中，如果确定可用，需要进一步将文件保存至工程目录中</li>
                  <li>
                    JetBrains IDEs：创建新的代码文件，需要先指定文件保存的位置，确认后会将代码块中的代码写入新的文件中
                    <img src={IMAGE_URL_MAP['431-7']} />
                    <img src={IMAGE_URL_MAP['431-8']} />
                    </li>
                </ul>
              </li>
            </ul>
          </li>
        </ol>

        <h3 id={CATALOG_MAP['44'].id}>{CATALOG_MAP['44'].title}</h3>
        <h4 id={CATALOG_MAP['441'].id}>{CATALOG_MAP['441'].title}</h4>
        <p>功能概述</p>
        <p>个性化定制代码续写策略。</p>
        <p>前提条件</p>
        <p>无</p>
        <p>操作指南</p>
        <p>点击插件菜单栏中的设置按钮，即可打开插件的个人设置界面。找到个人设置中的代码续写配置，按需配置是否开启和推荐策略。</p>
        <ol>
          <li>实时代码续写推荐模式：自动|单行优先|多行优先</li>
          <li>开启实时代码续写：开启|关闭
            <img src={IMAGE_URL_MAP['441-1']} />
          </li>
        </ol>

        <h4 id={CATALOG_MAP['442'].id}>{CATALOG_MAP['442'].title}</h4>
        <p>功能概述</p>
        <p>个性化定制单测代码生成所使用的框架。</p>
        <p>前提条件</p>
        <p>无</p>
        <p>操作指南</p>
        <p>点击插件菜单栏中的设置按钮，即可打开插件的个人设置界面。找到个人设置中的单测生成配置，按需配置各个编程语言的单测代码生成所使用的框架。</p>
        <p>配置成功后，再次唤起插件进行函数单测生成指令提问时，会使用指定的框架生成单测代码。</p>
        <img src={IMAGE_URL_MAP['442-1']} />
        <p>各语言单测框架支持范围如下：</p>
        <table>
          <thead>
            <tr>
              <th>编程语言</th>
              <th>单测框架</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>C/C++</td>
              <td>gtest</td>
            </tr>
            <tr>
              <td>Go</td>
              <td>gotests</td>
            </tr>
            <tr>
              <td rowSpan={2}>Java</td>
              <td>junit4</td>
            </tr>
            <tr>
              <td>junit5</td>
            </tr>
            <tr>
              <td rowSpan={2}>JavaScript</td>
              <td>jest</td>
            </tr>
            <tr>
              <td>mocha</td>
            </tr>
            <tr>
              <td rowSpan={2}>TypeScript</td>
              <td>jest</td>
            </tr>
            <tr>
              <td>mocha</td>
            </tr>
            <tr>
              <td rowSpan={2}>Python</td>
              <td>pytest</td>
            </tr>
            <tr>
              <td>unittest</td>
            </tr>
          </tbody>
        </table>
        <p>更多说明：个人配置区分编辑器，不同编辑器的配置相互独立，且保存在用户本地。</p>
      </div>
    </div>
  );
}

export default HelpCenter;
