import services from '@/services';
import { Button } from 'antd';
import { useEffect } from 'react';
import './index.css';

const LoginResult = () => {
  const search = decodeURIComponent(window.location.search);
  const searchParams = new URLSearchParams(search);
  const track: string = searchParams.get('track') || '';

  useEffect(() => {
    services.UserService.loginWithDevops({
      track,
      accessToken: searchParams.get('accessToken') || '',
      state_id: searchParams.get('state_id') || '',
      device: searchParams.get('device') || ''
    }).then((res) => {
      console.log(res);
    });
  }, []);

  // const handleReturn = () => {
  //   window.open(`vscode://cucloud.cucloud-ai-code-assistant`);
  // };

  // const extra =
  //   track !== 'vscode'
  //     ? [
  //         <Button
  //           className="return-button"
  //           key="buy"
  //           type="primary"
  //           size='large'
  //           onClick={handleReturn}
  //         >
  //           返回 VS Code
  //         </Button>
  //       ]
  //     : [];

  return (
    <div className="ai-assistant-login-result">
      <div className="assistant-logo"></div>
      <div className="result">
        <div className="success-icon"></div>
        <div className="result-title">登录成功</div>
        <div className="result-subtitle">可返回 IDE 客户端立即体验智能编程助手</div>
        {/* <div className="result-extra">{extra}</div> */}
      </div>
    </div>
  );
};

export default LoginResult;
