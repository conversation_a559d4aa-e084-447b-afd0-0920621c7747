@font-face {
  font-family: 'HYYa<PERSON>u<PERSON><PERSON>';
  src: url('@/assets/fonts/HYYaKuHei.ttf');
}

.intro-page {
  position: relative;
  min-width: 1280px;
  margin: 0 auto;
  background-color: #edf2f4;

  .banner {
    width: 100%;
    aspect-ratio: 1920 / 832;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: relative;

    .banner-img {
      width: 100%;
      height: 100%;
      background-size: cover;
    }

    .logo {
      width: 389px;
      height: 40px;
      position: absolute;
      cursor: pointer;
      left: 43px;
      top: 24px;
    }
    .login {
      width: 72px;
      height: 32px;
      font-size: 0.875rem;
      position: absolute;
      top: 24px;
      right: 36px;
      border: none;
      border-radius: 2px;
      background-color: #38acff;
      &:hover {
        background: #6cc7f8;
      }
      &:active {
        background: #1277c9;
      }
    }

    .intro {
      position: absolute;
      left: 51%;
      right: 0;
      margin-top: -4%;

      .title {
        font-size: 1.875rem;
        line-height: 4rem;
        letter-spacing: 1px;
        font-family: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Arial, Helvetica, sans-serif;
        font-weight: normal;
        color: #000000;
      }

      .bigTitle1,
      .bigTitle2 {
        font-size: 3.75rem;
        font-family: HYYaKuHei, HYYaKuHei;
        font-weight: normal;
        color: #000000;
        line-height: 4.875rem;
        letter-spacing: 2px;
      }
      .bigTitle2 {
        color: #1d99f3;
      }
      .desc {
        margin-top: 24px;
        font-size: 1.125rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.9);
        line-height: 2rem;
        letter-spacing: 1px;
      }
      .enter {
        margin-right: 20px;
        margin-top: 44px;
        width: 160px;
        height: 48px;
        border: none;
        transition: none;
        font-size: 16px;
        background: linear-gradient(
          270deg,
          #2f55f6 0%,
          #2782f5 33%,
          #1d99f3 70%,
          #1dd7f3 100%
        );
        &:hover {
          background: #6cc7f8;
        }
        &:active {
          background: #1277c9;
        }
      }

      .product-doc {
        width: 160px;
        height: 48px;
        border: 1px solid rgba(0,0,0,0.8);
        background: none;
        color: #000;
        font-size: 16px;
        box-shadow: none;

        &:hover {
          border-color: #1277c9;
          color: #1277c9;
        }
        &:active {
          background: #1277c9;
          color: #1277c9;
        }
      }
    }
  }

  .video-container {
    height: 787px;
    display: flex;
    justify-content: center;
    video {
      box-shadow: 0 6px 24px 10px rgba(0, 0, 0, 0.1);
      border-radius: 10px;
    }
  }

  .features {
    width: 100%;
    background-image: url('@/assets/intro/feature-bg.png');
    background-size: cover;
    background-position: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 72px;
    padding-bottom: 150px;
    .title {
      margin-bottom: 72px;
      font-size: 2.25rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 3.125rem;
    }
    .feature-cards {
      width: 1194px;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 32px 24px;
      .card {
        height: 244px;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 24px;
        background: #ffffff;
        box-shadow: 2px 2px 8px 2px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        opacity: 0.95;
        cursor: pointer;
        transition: all 0.28s ease-in-out;
        &:hover {
          transform: scale(1.05);
          box-shadow: 2px 2px 8px 2px rgba(0, 0, 0, 0.2);
        }
        .icon {
          width: 60px;
          height: 60px;
          margin-bottom: 16px;
        }
        .title {
          font-size: 1.125rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          color: rgba(0, 0, 0, 0.8);
          line-height: 1.5rem;
          letter-spacing: 1px;
          margin-bottom: 12px;
        }
        .desc {
          font-size: 0.875rem;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: rgba(0, 0, 0, 0.7);
          line-height: 1.75rem;
        }
      }
    }
  }

  .screen-shots {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-image: url('@/assets/intro/screenshoot-bg.png');
    background-size: cover;
    .ss-title {
      z-index: 10;
      margin-top: 72px;
      width: 100%;
      text-align: center;
      font-size: 2.25rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #000000;
      line-height: 3.125rem;
      margin-bottom: 72px;
    }
    .ss-card {
      width: 1194px;
      height: 428px;
      margin-bottom: 40px;
      z-index: 10;
      padding: 59px 64px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #ffffff;
      box-shadow: 2px 2px 8px 2px rgba(0, 0, 0, 0.06);
      border-radius: 8px;
      .card-img-container {
        width: 560px;
        height: 310px;
        position: relative;
        box-shadow: 0px 12px 24px 4px rgba(107, 121, 133, 0.06);
        border-radius: 8px;
        border: 1px solid #d9dee1;
        overflow: hidden;
        .card-img {
          width: 560px;
          height: 310px;
          background-size: cover;
        }
      }

      .card-text-block {
        width: 431px;
        .title {
          font-size: 3rem;
          font-family: HYYaKuHei, HYYaKuHei;
          font-weight: normal;
          color: #1d99f3;
          line-height: 3.625rem;
          letter-spacing: 1px;
          margin-bottom: 12px;
        }
        .divider {
          width: 348px;
          height: 2px;
          background: linear-gradient(
            90deg,
            #2180f7 0%,
            rgba(20, 195, 249, 0.1) 100%
          );
          margin-bottom: 22px;
        }
        .points {
          padding-left: 16px;
          .point {
            width: max-content;
            max-width: 450px;
            font-size: 1rem;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.9);
            line-height: 2rem;
          }
        }
      }
      &:nth-child(odd) {
        .card-img-container {
          order: 2;
        }
      }
    }
  }

  @media (max-width: 1700px) {
    .banner .intro {
      .bigTitle1,
      .bigTitle2 {
        font-size: 3rem;
        line-height: 4rem;
      }
    }
  }

  @media (max-width: 1439px) {
    .video-container {
      transform: scale(0.85);
    }
  }

  @media (max-width: 1380px) {
    .banner .intro {
      .bigTitle1,
      .bigTitle2 {
        font-size: 2.5rem;
        line-height: 3.5rem;
      }
    }
  }
}
