// import services from '@/services';
import { useState } from 'react';
import './download-section.less';

type Ide = 'vscode' | 'jetbrains';

const VScodeGuide = {
  name: 'VSCode',
  logo: require('@/assets/intro/vscode.png'),
  guideSteps: [
    [
      {
        type: 'text',
        content:
          '下载并安装 VisualStudioCode 1.75.0 及以上版本。点击左侧「下载最新版」获取最新智能编程助手插件。'
      }
    ],
    [
      {
        type: 'text',
        content:
          '打开 VisualStudioCode 后，单击扩展，找到更多按钮，在下拉菜单中单击从 VSIX 安装，选择刚刚下载的 VSIX 插件文件，点击确认进行安装。'
      },
      { type: 'image', content: require('@/assets/intro/vscode-s1.png') }
    ],
    [
      {
        type: 'text',
        content:
          '安装完成后可看到成功安装提示，左侧工具栏出现联通云智能编程助手插件入口。'
      },
      { type: 'image', content: require('@/assets/intro/vscode-s2.png') }
    ]
  ]
};

const JetBrainsGuide = {
  name: 'JetBrains IDEs',
  logo: require('@/assets/intro/jetbrains.png'),
  guideSteps: [
    [
      {
        type: 'text',
        content:
          '下载并安装 JetBrainsIDEs（如：WebStorm） 2023.01 及以上版本。点击左侧「下载最新版」获取最新智能编程助手插件。'
      }
    ],
    [
      {
        type: 'text',
        content:
          '打开 WebStorm 后，在设置下拉菜单中在设置下拉菜单中单击插件，打开插件弹窗。'
      },
      { type: 'image', content: require('@/assets/intro/jetbrains-s1.png') }
    ],
    [
      {
        type: 'text',
        content:
          '在插件弹窗的设置下拉菜单中，单击从磁盘安装插件，选择刚刚下载的 zip 文件安装。'
      },
      { type: 'image', content: require('@/assets/intro/jetbrains-s2.png') }
    ],
    [
      {
        type: 'text',
        content:
          '安装完成后，点击确定关闭插件弹窗，在 WebStorm 右侧插件扩展区会出现联通云智能编程助手插件入口。'
      }
    ]
  ]
};

export function DownloadSection() {
  const [currentIde, setCurrentIde] = useState<Ide>('vscode');

  const guide = currentIde === 'vscode' ? VScodeGuide : JetBrainsGuide;

  const getButtonClassName = (type: Ide) => {
    if (type === currentIde) {
      return 'tab-button is-active';
    }
    return 'tab-button';
  };

  const handleDownload = () => {
    // services.Version.getLastestVersion(currentIde).then((res) => {
    //   window.open(res.data.download_url, '_self');
    // });
    window.open(
      'https://alidocs.dingtalk.com/i/nodes/KGZLxjv9VG327pNvUnYX0lP1V6EDybno',
      '_blank'
    );
  };

  return (
    <div id="sectionDownload" className="download-section">
      <h2 className="download-section-title">本地 IDE 中快速安装及使用</h2>
      <div className="tab-buttons">
        <div
          className={getButtonClassName('vscode')}
          onClick={() => setCurrentIde('vscode')}
        >
          VSCode
        </div>
        <div
          className={getButtonClassName('jetbrains')}
          onClick={() => setCurrentIde('jetbrains')}
        >
          JetBrains IDEs
        </div>
      </div>
      <div className="download-box">
        <div className="download-box-left">
          <img className="ide-logo" src={guide.logo}></img>
          <div className="ide-name">{guide.name} 插件</div>
          <div className="download-button" onClick={handleDownload}>
            下载最新版
          </div>
          <div style={{ marginTop: 12 }}>
            {/* <a
              className="other-version"
              href="https://alidocs.dingtalk.com/i/nodes/KGZLxjv9VG327pNvUnYX0lP1V6EDybno"
              target="_blank"
              rel="noopener noreferrer"
            >
              其他版本
            </a> */}
          </div>
        </div>
        <div className="download-box-right">
          <div className="install-guide">
            {guide.guideSteps.map((stepItems, index) => {
              return (
                <div className="install-guide-step" key={index}>
                  <div className="install-guide-step-linen">
                    <div className={`step-icon icon-${index + 1}`}>
                      0{index + 1}
                    </div>
                    <div className="step-line"></div>
                  </div>
                  <div className="install-guide-step-content">
                    {stepItems.map((item, index$) => {
                      if (item.type === 'text') {
                        return (
                          <div className="install-guide-text" key={index$}>
                            {item.content}
                          </div>
                        );
                      }

                      return (
                        <img
                          key={index$}
                          className="install-guide-image"
                          src={item.content}
                        ></img>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
