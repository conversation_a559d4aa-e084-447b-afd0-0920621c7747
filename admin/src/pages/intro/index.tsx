import banner from '@/assets/intro/banner.jpg';
import logo from '@/assets/intro/logo.png';
import { Helmet } from '@umijs/max';
import { Button } from 'antd';
import { DownloadSection } from './components/download-section';
import './index.less';

const page = {
  title: '联通云智能编程助手',
  banner,
  intro: {
    title: '联通云智能编程助手',
    bigTitle1: '更智能更专业的多功能',
    bigTitle2: '辅助编程工具',
    desc: '利用人工智能技术为程序员提供代码自动生成、补全、修复和优化等功能'
  },
  video: null,
  featureTitle: '助力研发团队更高效的编码',
  features: [
    {
      icon: require('@/assets/intro/icon-1.png'),
      title: '智能上下文感知能力',
      desc: '插件端与模型层协同优化代码跨文件上下文感知能力，使得生成的代码能高度契合当前代码库的场景，为生成代码的准确性和采纳率提供有力支撑。'
    },
    {
      icon: require('@/assets/intro/icon-2.png'),
      title: '适配主流编辑器',
      desc: '定制适配多种 IDE 的原生主题与交互模式，让开发者尽享 IDE 的原生视觉效果与交互体验，完美符合开发者的使用习惯，带来流畅的开发感受。'
    },
    {
      icon: require('@/assets/intro/icon-3.png'),
      title: '灵活的企业定制和部署方案',
      desc: '提供丰富多样的面向企业客户的方案，同时还可提供企业个性化方案，给予企业灵活选择的空间，加速企业内智能研发的规模化落地进程。'
    }
  ],
  screenshotTitle: '沉浸式辅助编码体验',
  cards: [
    {
      img: require('@/assets/intro/card01.jpeg'),
      title: '实时代码续写',
      points: [
        '行级代码续写：支持在代码编写过程中实时根据当前编程语言和当前行的上下文智能推荐当前行的续写代码',
        '块级代码续写：支持在代码编写过程中实时根据当前编程语言和当前文件上下文智能推荐块级多行代码'
      ]
    },
    {
      img: require('@/assets/intro/card02.png'),
      title: '智能代码生成',
      points: [
        '单元测试生成：支持生成各种编程语言主流单测框架的单测代码',
        '代码注释生成：支持生成函数注释和函数内的行间注释',
        '代码优化生成：支持对函数生成修复或优化代码及建议'
      ]
    },
    {
      img: require('@/assets/intro/card03.png'),
      title: '智能问答',
      points: [
        '自由智能问答：支持多轮连续问答和会话历史清除，可展示文本、链接、代码等应答内容，并可进行复制、采纳等操作',
        '代码解释问答：支持对选中的代码进行智能问答，可输出代码解释和优化建议等内容',
      ]
    },
    {
      img: require('@/assets/intro/card04.png'),
      title: '个性化配置',
      points: [
        '代码续写配置：支持开启/关闭代码续写和指定行级/块级优先',
        '单测生成配置：支持按语言指定生成的单测代码所使用的框架'
      ]
    }
  ]
};

export default function IntroPage() {
  const handleDownload = () => {
    location.href = '/admin/download'
  };

  const handleOpenDoc = () => {
    // 哈尔滨环境
    // window.open('https://alidocs.dingtalk.com/i/nodes/N7dx2rn0JbZ5LzGecxg3NK2nJMGjLRb3?doc_type=wiki_doc', '_blank')

    // 骨干云环境
    // window.open('https://alidocs.dingtalk.com/i/nodes/qnYMoO1rWxDy4XRdsqQZ22ZqW47Z3je9', '_blank')
    location.href = '/admin/help-center'
  }

  return (
    <div className="intro-page">
      <Helmet>
        <title>{page.title}</title>
      </Helmet>
      <div className="banner">
        <img src={page.banner} alt="banner" className="banner-img" />
        <img src={logo} alt="logo" className="logo" />
        <div className="intro">
          <div className="title">{page.intro.title}</div>
          <div className="bigTitle1">{page.intro.bigTitle1}</div>
          <div className="bigTitle2">{page.intro.bigTitle2}</div>
          <div className="desc">{page.intro.desc}</div>
          <div>
            <Button
              type="primary"
              className="enter"
              onClick={handleDownload}
            >
              点击下载
            </Button>
            <Button
              type="primary"
              className="product-doc"
              onClick={handleOpenDoc}
            >
              查看产品文档
            </Button>
          </div>
        </div>
      </div>

      <DownloadSection />

      {!!page.video && (
        <div className="video-container">
          <video width="1262" height="727" controls>
            <source src={page.video} type="video/mp4" />
          </video>
        </div>
      )}

      <div className="features">
        <div className="title">{page.featureTitle}</div>
        <div className="feature-cards" style={{ padding: '80px 0'}}>
          {page.features.map((feature, index) => (
            <div className="card" key={index}>
              <img src={feature.icon} alt="icon" className="icon" />
              <div className="title">{feature.title}</div>
              <div className="desc">{feature.desc}</div>
            </div>
          ))}
        </div>
      </div>

      {!!page.cards.length && (
        <div className="screen-shots">
          <div className="ss-title">{page.screenshotTitle}</div>
          {page.cards.map((card, index) => (
            <div className="ss-card" key={index}>
              <div className="card-img-container">
                <img src={card.img} className="card-img" />
              </div>
              <div className="card-text-block">
                <div className="title">{card.title}</div>
                <div className="divider" />
                <ul className="points">
                  {card.points.map((point, pindex) => (
                    <li className="point" key={pindex}>
                      {point}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
