import { request } from '@umijs/max';

/**
 * 使用DevOps登录
 *
 * @param data 登录所需参数
 * @param data.accessToken 访问令牌
 * @param data.track 跟踪信息
 * @param data.device 设备信息
 * @param data.state_id 状态ID
 * @returns 登录结果
 */
export async function loginWithDevops(data: {
  accessToken: string;
  track: string;
  device: string;
  state_id: string;
}) {
  return request('/console/api/devops/login', {
    method: 'POST',
    data
  });
}
