const users = [
  { id: 0, name: '<PERSON><PERSON>', nickName: 'U', gender: 'MA<PERSON>' },
  { id: 1, name: '<PERSON>', nickName: 'B', gender: 'FEMALE' },
];

export default {
  'GET /api/v1/queryUserList': (req: any, res: any) => {
    res.json({
      success: true,
      data: { list: users },
      errorCode: 0,
    });
  },
  'PUT /api/v1/user/': (req: any, res: any) => {
    res.json({
      success: true,
      errorCode: 0,
    });
  },
};
