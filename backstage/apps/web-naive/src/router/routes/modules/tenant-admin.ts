import type { RouteRecordRaw } from 'vue-router';
import { MdiCompany } from '@vben/icons'
import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';
import { RoleType } from '#/constant/role';
const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: MdiCompany,
      order: 100,
      title: $t('page.tenantAdmin.title'),
      hideChildrenInMenu: true,
      authority: [RoleType.SUPER_ADMIN],
    },
    name: 'TenantAdmin',
    path: '/tenant-admin',
    redirect: '/tenant-admin/index',
    children: [
      {
        name: 'TenantAdminIndex',
        path: '/tenant-admin/index',
        component: () => import('#/views/tenant-admin/index.vue'),
        meta: {
          affixTab: true,
          title: $t('page.tenantAdmin.title'),
        },
      }
    ],
  },
];

export default routes;
