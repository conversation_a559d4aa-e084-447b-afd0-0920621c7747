import type { RouteRecordRaw } from 'vue-router';
import { MdiUsers } from '@vben/icons'
import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';
import { RoleType } from '#/constant/role';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: MdiUsers,
      order: 2,
      title: $t('page.memberAdmin.title'),
      hideChildrenInMenu: true,
      // hideInMenu: import.meta.env.VITE_PUBLISH_VERSION === 'herb',
      authority: [RoleType.NOONE],
    },
    name: 'MemberAdmin',
    path: '/member-admin',
    redirect: '/member-admin/index',
    children: [
      {
        name: 'MemberAdminIndex',
        path: '/member-admin/index',
        component: () => import('#/views/member-admin/index.vue'),
        meta: {
          affixTab: true,
          title: $t('page.memberAdmin.title'),
        },
      }
    ],
  },
];

export default routes;
