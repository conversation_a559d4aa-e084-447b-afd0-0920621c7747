import type { RouteRecordRaw } from 'vue-router';
import { MdiDocumentMitiple } from '@vben/icons'
import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';
import { RoleType } from '#/constant/role';
const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: MdiDocumentMitiple,
      // order: 100,
      title: $t('page.knowledgeManage.title'),
      hideChildrenInMenu: true,
      authority: [RoleType.SUPER_ADMIN, RoleType.TENANT_ADMIN],
    },
    name: 'KnowledgeManage',
    path: '/knowledge-manage',
    redirect: '/knowledge-manage/index',
    children: [
      {
        name: 'KnowledgeManageIndex',
        path: '/knowledge-manage/index',
        component: () => import('#/views/knowledge-manage/index.vue'),
        meta: {
          affixTab: true,
          title: $t('page.knowledgeManage.title'),
        },
      }
    ],
  },
];

export default routes;
