import type { RouteRecordRaw } from 'vue-router';
import { MdiData } from '@vben/icons'
import { BasicLayout } from '#/layouts';
import { RoleType } from '#/constant/role';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: MdiData,
      order: 3,
      title: '数据看板',
      hideChildrenInMenu: true,
      authority: [RoleType.SUPER_ADMIN],
    },
    name: 'DataAdmin',
    path: '/data-admin',
    redirect: '/data-admin/index',
    children: [
      {
        name: 'DataAdminIndex',
        path: '/data-admin/index',
        component: () => import('#/views/data-admin/index.vue'),
        meta: {
          affixTab: true,
          title: '数据看板',
        },
      }
    ],
  },
];

export default routes;
