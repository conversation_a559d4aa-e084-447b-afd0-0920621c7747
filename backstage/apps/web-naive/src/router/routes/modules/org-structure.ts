import type { RouteRecordRaw } from 'vue-router';
import { MdiTree } from '@vben/icons'
import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';
import { RoleType } from '#/constant/role';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: MdiTree,
      order: 1,
      title: $t('page.orgStructure.title'),
      hideChildrenInMenu: true,
      // hideInMenu: import.meta.env.VITE_PUBLISH_VERSION === 'herb',
      authority: [RoleType.NOONE],
    },
    name: 'OrgStruct',
    path: '/org-structure',
    redirect: '/org-structure/index',
    children: [
      {
        name: 'OrgStructIndex',
        path: '/org-structure/index',
        component: () => import('#/views/org-structure/index.vue'),
        meta: {
          affixTab: true,
          title: $t('page.orgStructure.title'),
        },
      }
    ],
  },
];

export default routes;
