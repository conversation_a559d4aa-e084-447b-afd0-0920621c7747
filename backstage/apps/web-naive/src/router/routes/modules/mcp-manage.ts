import type { RouteRecordRaw } from 'vue-router';
import { MdiToolbox } from '@vben/icons'
import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';
import { RoleType } from '#/constant/role';
const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: MdiToolbox,
      // order: 100,
      title: $t('page.mcpManage.title'),
      hideChildrenInMenu: true,
      authority: [RoleType.SUPER_ADMIN, RoleType.TENANT_ADMIN],
    },
    name: 'McpManage',
    path: '/mcp-manage',
    redirect: '/mcp-manage/index',
    children: [
      {
        name: 'McpManageIndex',
        path: '/mcp-manage/index',
        component: () => import('#/views/mcp-manage/index.vue'),
        meta: {
          affixTab: true,
          title: $t('page.mcpManage.title'),
        },
      }
    ],
  },
];

export default routes;
