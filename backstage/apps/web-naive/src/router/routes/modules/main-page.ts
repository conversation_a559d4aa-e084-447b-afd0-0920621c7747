import type { RouteRecordRaw } from 'vue-router';
import { MdiHome } from '@vben/icons'
import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
 {
  component: BasicLayout,
  meta: {
   icon: MdiHome,
   order: -1,
   title: $t('page.mainPage.title'),
   hideChildrenInMenu: true,
  },
  name: 'MainPage',
  path: '/main-page',
  children: [
   {
    name: 'MainPageIndex',
    path: '/main-page',
    component: () => import('#/views/main-page/index.vue'),
    meta: {
     affixTab: true,
     title: $t('page.mainPage.title'),
    },
   }
  ],
 },
];

export default routes;
