import type { RouteRecordRaw } from 'vue-router';
import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
 {
  component: BasicLayout,
  meta: {
   title: '安装包上传',
   hideChildrenInMenu: true,
   hideInMenu: true,
  },
  name: 'PackageAdmin',
  path: '/package-admin',
  redirect: '/package-admin/index',
  children: [
   {
    name: 'PackageAdminIndex',
    path: '/package-admin/index',
    component: () => import('#/views/package-admin/index.vue'),
    meta: {
     affixTab: true,
     title: '安装包上传',
    },
   }
  ],
 },
];

export default routes;
