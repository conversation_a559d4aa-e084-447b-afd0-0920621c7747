import type { RouteRecordRaw } from 'vue-router';

import { DEFAULT_HOME_PATH, LOGIN_PATH } from '@vben/constants';

import { AuthPageLayout } from '#/layouts';
import { $t } from '#/locales';
import Login from '#/views/_core/authentication/login.vue';

import { MdiData } from '@vben/icons'
import { BasicLayout } from '#/layouts';

/** 全局404页面 */
const fallbackNotFoundRoute: RouteRecordRaw = {
  component: () => import('#/views/_core/fallback/not-found.vue'),
  meta: {
    hideInBreadcrumb: true,
    hideInMenu: true,
    hideInTab: true,
    title: '404',
  },
  name: 'FallbackNotFound',
  path: '/:path(.*)*',
};

/** 基本路由，这些路由是必须存在的 */
const coreRoutes: RouteRecordRaw[] = [
  {
    meta: {
      title: 'Root',
    },
    name: 'Root',
    path: '/',
    redirect: DEFAULT_HOME_PATH,
  },
  {
    meta: {
      title: '登陆成功',
      hideInMenu: true,
      ignoreAccess: true,
    },
    name: 'PluginLogin',
    path: '/plugin-login',
    component: () => import('#/views/plugin-login/index.vue'),
  },
  {
    component: AuthPageLayout,
    meta: {
      hideInTab: true,
      title: 'Authentication',
    },
    name: 'Authentication',
    path: '/auth',
    redirect: LOGIN_PATH,
    children: [
      {
        name: 'Login',
        path: 'login',
        component: Login,
        meta: {
          title: '登录',
        },
      },
      // {
      //   name: 'ForgetPassword',
      //   path: 'forget-password',
      //   component: () =>
      //     import('#/views/_core/authentication/forget-password.vue'),
      //   meta: {
      //     title: $t('page.auth.forgetPassword'),
      //   },
      // },
      {
        name: 'Register',
        path: 'register',
        component: () => import('#/views/_core/authentication/register.vue'),
        meta: {
          title: '注册',
        },
      },
    ],
  },
  {
    meta: {
      title: '接受邀请',
      hideInMenu: true,
      ignoreAccess: true,
    },
    name: 'UserActivate',
    path: '/user/activate',
    redirect: '/invite-accept/index',
  },
  {
    meta: {
      order: -1,
      title: $t('page.inviteAccept.title'),
      hideInMenu: true,
    },
    name: 'InviteAccept',
    path: '/invite-accept/index',
    component: () => import('#/views/_core/invite-accept/index.vue'),
  },
  {
    meta: {
      order: -1,
      title: $t('page.redirect.title'),
      hideInMenu: true,
    },
    name: 'ExternalAuth',
    path: '/external-auth',
    component: () => import('#/views/_core/external-auth/index.vue'),
  },

  // {
  //   component: BasicLayout,
  //   meta: {
  //    icon: MdiData,
  //    order: 3,
  //    title: $t('page.dataAdmin.title'),
  //    hideChildrenInMenu: true,
  //   },
  //   name: 'DataAdmin',
  //   path: '/data-admin',
  //   redirect: '/data-admin/index',
  //   children: [
  //    {
  //     name: 'DataAdminIndex',
  //     path: '/data-admin/index',
  //     component: () => import('#/views/data-admin/index.vue'),
  //     meta: {
  //      affixTab: true,
  //      title: $t('page.dataAdmin.title'),
  //     },
  //    }
  //   ],
  //  },
];

export { coreRoutes, fallbackNotFoundRoute };
