import CryptoJs from 'crypto-js';

const Ket_Code = 'abcdefgabcdefg12'

export function encrypt(content: string) {
  let sKey = CryptoJs.enc.Utf8.parse(Ket_Code);
  let sContent = CryptoJs.enc.Utf8.parse(content);
  let encrypted = CryptoJs.AES.encrypt(sContent, sKey, { mode: CryptoJs.mode.ECB, padding: CryptoJs.pad.Pkcs7 });
  return encrypted.toString();
}

export function decrypt(word: string) {
  let key = CryptoJs.enc.Utf8.parse(Ket_Code);
  let decrypt;
  try {
    decrypt = CryptoJs.AES.decrypt(word, key, { mode: CryptoJs.mode.ECB, padding: CryptoJs.pad.Pkcs7 });
  } catch (e) {
    return word;
  }
  return CryptoJs.enc.Utf8.stringify(decrypt).toString();
}

export interface DevopsToken {
  token: string;
  loginUrl: string;
}
