import { useAccessStore } from '@vben/stores';
import { isUnifiedPlatform } from '@vben/constants'

const accessStore = useAccessStore()

export function getKnowledgeBaseUrl() {
  if (isUnifiedPlatform() && accessStore.devopsLoginUrl) {
    return getIpFromUrl(accessStore.devopsLoginUrl)
  }

  return ''
}

export function getIpFromUrl(url: string): string {
  const match = url.match(/^https?:\/\/[^\/]+/);
  if (match) {
    return match[0]
  }
  return ''
}
