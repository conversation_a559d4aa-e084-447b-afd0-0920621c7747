import { useAccessStore } from '@vben/stores';
import { getUserInfoApi } from '#/api';

export async function storeGlobalInfo(accessToken: string) {
  const accessStore = useAccessStore()

  if (!accessToken) return;

  // 将 accessToken 存储到 accessStore 中
  accessStore.setAccessToken(accessToken);

  const res = await getUserInfoApi()
  const codes = res?.role ? [res.role] : []
  accessStore.setAccessCodes(codes);

  if (accessStore.loginExpired) {
    accessStore.setLoginExpired(false);
  }
}
