export function getParamFromSearch(locationSearch: string, paramName: string): string {
  const params = new URLSearchParams(locationSearch);

  // 1. 如果externalToken或loginUrl直接在search里，直接返回
  let paramVal = params.get(paramName);
  if (paramVal) {
    return paramVal.replace(/ /g, "+")
  }

  // 2. 否则尝试从redirect参数中获取
  let redirect = params.get("redirect");
  if (redirect) {
    // 多次解码直到出现externalToken=或loginUrl=
    let decoded = redirect;
    let tries = 0;
    while (!(decoded.includes(`${paramName}=`)) && tries < 5) {
      decoded = decodeURIComponent(decoded);
      tries++;
    }

    // 用URLSearchParams再次解析
    const queryIndex = decoded.indexOf("?");
    if (queryIndex !== -1) {
      const innerQuery = decoded.slice(queryIndex);
      const innerParams = new URLSearchParams(innerQuery);
      // 优先取更内层的externalToken/loginUrl
      let innerParam = innerParams.get(paramName);
      if (innerParam) paramVal = innerParam.replace(/ /g, "+");
    }
  }

  return paramVal || '';
}
