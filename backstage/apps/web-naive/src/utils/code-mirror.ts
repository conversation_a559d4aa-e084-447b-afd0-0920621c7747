import CodeMirror from 'codemirror';
import 'codemirror/mode/javascript/javascript';
import 'codemirror/theme/monokai.css';

// 定义编辑器配置接口
interface CodeMirrorConfig extends CodeMirror.EditorConfiguration {
  placeholder?: string;
}

// 创建 CodeMirror 5 编辑器
export function createCodeMirror5Editor(
  container: HTMLElement,
  config: CodeMirrorConfig = {}
): CodeMirror.Editor {
  if (!container || !(container instanceof HTMLElement)) {
    throw new Error('容器元素必须是有效的 HTMLElement');
  }

  // 设置默认配置
  const defaultConfig: CodeMirror.EditorConfiguration = {
    mode: 'javascript',
    theme: 'monokai',
    lineNumbers: true,
    value: 'function hello() {\n  console.log("Hello, CodeMirror!");\n}',
    ...config
  };

  // 创建编辑器实例
  const editor = CodeMirror(container, defaultConfig);

  return editor;
}
