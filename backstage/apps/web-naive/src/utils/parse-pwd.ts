/*
 * @Author: <PERSON>
 * @Date: 2024-04-28 16:16:55
 * @Last Modified by: wangzj533
 * @Last Modified time: 2025-01-22 14:17:35
 * @Description: 密码加解密
 */
import JSEncrypt from 'jsencrypt'
import { z } from '@vben/common-ui';

export const PWD_REG =
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$@$!%*?&])[A-Za-z\d$@$!%*?&]{8,}/

/**
 * 获取加密器
 *
 * @returns 返回JSEncrypt加密器实例
 */
async function getEncryptor() {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(import.meta.env.VITE_RSA_PUBLIC_KEY)
  return encryptor
}

/**
 * 加密密码
 *
 * @param pwd 需要加密的密码
 * @returns 返回加密后的密码
 */
export async function encryptPwd(pwd?: string) {
  if (!pwd) {
    return ''
  }

  const encryptor = await getEncryptor()
  return encryptor.encrypt(pwd) || ''
}

/**
 * 解密密码
 *
 * @param pwd 待解密的密码
 * @returns 返回解密后的密码
 */
export async function decodePwd(pwd: string) {
  const encryptor = await getEncryptor()
  return encryptor.decrypt(pwd)
}

/**
 * 获取密码校验规则
 *
 * @param errMsg 错误信息，默认为'请输入密码'
 * @returns 返回密码校验规则数组
 */
export function getPWDRules(errMsg = '请输入密码') {
  return z.string().min(1, { message: errMsg }).regex(PWD_REG, {
    message: '密码必须包含大小写字母、数字和特殊字符'
  })
}
