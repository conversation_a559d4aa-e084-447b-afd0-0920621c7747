<template>
  <div>
    <n-flex vertical class="pt-[100px]" align="center">
      <h1 class="text-2xl font-medium mb-8">联通云智能编程助手</h1>
      <div>您的企业<span class="text-[#c91243]">{{ activateModel.tenantName }}</span>邀请您使用联通云智能编程助手</div>
      <div>您信息将用来帮助企业管理者完成您的身份识别，请认真填写</div>
      <n-form ref="formRef" class="w-[500px]" :model="activateModel" :rules="rules">
        <n-form-item label="企业名称" path="tenantName">
          <n-input disabled v-model:value="activateModel.tenantName" />
        </n-form-item>
        <n-form-item label="部门名称" path="departmentName">
          <n-input disabled v-model:value="activateModel.departmentName" />
        </n-form-item>
        <n-form-item label="您的角色" path="role">
          <n-select disabled :options="PermissionArr" v-model:value="activateModel.role" />
        </n-form-item>
        <n-form-item label="用户名" path="name">
          <n-input placeholder="请输入您的用户名" v-model:value="activateModel.name" />
        </n-form-item>
        <n-form-item label="您的邮箱" path="email">
          <n-input placeholder="请填写您的邮箱" v-model:value="activateModel.email" />
        </n-form-item>
        <!-- <n-form-item label="密码" path="password">
          <n-input placeholder="包含大小写字母、数字和特殊字符" type="password" v-model:value="activateModel.password" />
        </n-form-item>
        <n-form-item label="确认密码" path="confirmPassword">
          <n-input placeholder="请确认密码" type="password" v-model:value="activateModel.confirmPassword" />
        </n-form-item> -->
      </n-form>
      <n-button class="w-[500px]" type="primary" @click="activateUserInfo">确认加入</n-button>
    </n-flex>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { NFlex, NButton, NForm, NFormItem, NInput, NSelect, type FormRules, type FormInst } from 'naive-ui'
import { useAccessStore } from '@vben/stores';
import { useRouter } from 'vue-router';
import { DEFAULT_HOME_PATH } from '@vben/constants';
import { PermissionArr } from '#/constant/role'
import { getActivateInfoApi, activateUserApi, loginApi } from '#/api'
import { encryptPwd, PWD_REG } from '#/utils/parse-pwd';
const router = useRouter()
const accessStore = useAccessStore()
const formRef = ref<FormInst | null>(null)
const activateModel = ref({
  tenantName: '',
  tenantId: '',
  departmentName: '',
  role: '',
  name: '',
  email: '',
})
const rules: FormRules = {
  name: {
    required: true,
    message: '请输入用户名',
    trigger: 'blur'
  },
  email: [{
    required: true,
    message: '请输入邮箱',
    trigger: 'blur'
  }, {
    message: '请输入邮箱',
    trigger: 'blur',
    type: 'email'
  }],
  // password: [
  //   {
  //     required: true,
  //     message: '请输入密码',
  //     trigger: 'blur'
  //   },
  //   {
  //     validator: (_, value) => {
  //       return PWD_REG.test(value)
  //     },
  //     message: '密码必须包含大小写字母、数字和特殊字符',
  //     trigger: 'blur'
  //   }
  // ],
  // confirmPassword: [
  //   {
  //     required: true,
  //     message: '请确认密码',
  //     trigger: 'blur'
  //   },
  //   {
  //     validator: (_, value) => {
  //       return value === activateModel.value.password
  //     },
  //     message: '两次密码不一致',
  //     trigger: 'blur'
  //   }
  // ]
}
onMounted(async () => {
  const params = new URLSearchParams(window.location.search)
  const res = await getActivateInfoApi(String(params.get('code')))
  activateModel.value = {
    ...res
  }
})
const activateUserInfo = async () => {
  formRef?.value?.validate().then(async (errors) => {
    if (errors.warnings) {
      return
    }
    const { name, email } = activateModel.value
    await activateUserApi({
      name,
      // password: await encryptPwd(password),
      email,
      code: String(new URLSearchParams(window.location.search).get('code'))
    })
    window.location.href = window.location.origin + '/portal'
    // const accessToken = await loginApi({
    //   email,
    //   // password: await encryptPwd(password),
    //   tenantId: activateModel.value.tenantId
    // })
    // if (accessToken) {
    //   // 将 accessToken 存储到 accessStore 中
    //   accessStore.setAccessToken(accessToken);

    //   if (accessStore.loginExpired) {
    //     accessStore.setLoginExpired(false);
    //     router.push(DEFAULT_HOME_PATH);
    //   }
    //   else {
    //     router.push(DEFAULT_HOME_PATH);
    //   }
    // }
  })


}
</script>

<style scoped>
:global(#settingTrigger) {
  display: none !important;
}
</style>
