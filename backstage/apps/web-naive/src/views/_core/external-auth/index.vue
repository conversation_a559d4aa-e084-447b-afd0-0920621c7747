<template>
</template>
<script setup lang="ts">
import { getParamFromSearch, storeGlobalInfo } from '#/utils'
import { onMounted } from 'vue'
import { useRouter } from 'vue-router';
import { LOGIN_PATH, DEFAULT_HOME_PATH, CLIENT_ID } from '@vben/constants';
import { getAccessTokenApi } from '#/api'
import { useAccessStore } from '@vben/stores';
import { useMessage } from 'naive-ui'

const router = useRouter()
const accessStore = useAccessStore()
const message = useMessage()

const saveDevopsTokenFromSearch = () => {
  const search = window.location.search;
  const _externalToken = getParamFromSearch(search, 'externalToken');
  const _accessToken = getParamFromSearch(search, 'accessToken');
  const _loginUrl = decodeURIComponent(getParamFromSearch(search, 'loginUrl'));
  const token = _externalToken || _accessToken;
  if (!token) {
    return false;
  }

  accessStore.setDevopsLoginInfo(_loginUrl, token);

  return true;
}

onMounted(async () => {
  if (accessStore.accessToken) {
    router.replace(DEFAULT_HOME_PATH)
    return;
  }

  if (!saveDevopsTokenFromSearch()) {
    router.replace(LOGIN_PATH)
    return;
  }

  const res = await getAccessTokenApi(accessStore.devopsToken || '');
  const accessToken = res?.data?.data;

  if (!accessToken) {
    message.error(res?.data?.message);
    window.location.href = `${accessStore.devopsLoginUrl}?client_id=${CLIENT_ID}&redirect_uri=${encodeURIComponent(window.location.origin + '/management/')}`;
    return;
  }

  await storeGlobalInfo(accessToken);

  router.replace({ path: DEFAULT_HOME_PATH, query: {} })
})

</script>
