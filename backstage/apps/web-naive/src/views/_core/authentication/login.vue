<script lang="ts" setup>
import type { Recordable } from '@vben/types';
import { DEFAULT_HOME_PATH } from '@vben/constants';
// import { useRouter } from 'vue-router';
import type { VbenFormSchema } from '@vben/common-ui';
import TenantSelect from './tenant-select.vue'
import { loginApi, getTenantListApi } from '#/api';

import { computed, ref } from 'vue';

import { AuthenticationLogin, z } from '@vben/common-ui';
// import { $t } from '@vben/locales';
import { useRouter } from 'vue-router';
import { encryptPwd } from '#/utils/parse-pwd'
import { storeGlobalInfo } from '#/utils/store-global-info'

const router = useRouter()
const step = ref(1)
defineOptions({ name: 'Login' })

const tenantList = ref<{ label?: string, value?: string }[] | undefined>([])
const loginLoading = ref(false)
const confirmLoading = ref(false)
const loginInfo = ref<{
  email?: string
  password?: string
}>({
  email: '',
  password: ''
})
const validateInfo = async (params: Recordable<any>) => {
  try {
    loginLoading.value = true;
    loginInfo.value = {
      ...params
    }
    const tenantListRes = await getTenantListApi({ ...params, password: await encryptPwd(params.password) })
    if (tenantListRes) {
      tenantList.value = tenantListRes?.map(item => {
        return {
          label: item.tenantName,
          value: item.tenantId,
        }
      })
      step.value = 2
    }
  } finally {
    loginLoading.value = false
  }
}
const doLogin = async (params: Recordable<any>,
  _?: () => Promise<void> | void,) => {
  // 异步处理用户登录操作并获取 accessToken
  // let userInfo: null | UserInfo = null;
  try {
    confirmLoading.value = true;
    const { password } = loginInfo.value
    const accessToken = await loginApi({ ...params, ...loginInfo.value, password: await encryptPwd(password) });
    // 如果成功获取到 accessToken
    if (accessToken) {
      await storeGlobalInfo(accessToken);
      router.push(DEFAULT_HOME_PATH);


      // const userInfo = await getUserInfoApi()
      // 获取用户信息并存储到 accessStore 中
      // const [accessCodes] = await Promise.all([
      //   // fetchUserInfo(),
      //   getAccessCodesApi(),
      // ]);


      // if (userInfo?.realName) {
      //   notification.success({
      //     content: $t('authentication.loginSuccess'),
      //     description: `${$t('authentication.loginSuccessDesc')}:${userInfo?.realName}`,
      //     duration: 3000,
      //   });
      // }
    } else {
      confirmLoading.value = false;
    }
  } finally {
    confirmLoading.value = false;
  }
}

const back = () => {
  step.value = 1
}

const formSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入邮箱',
      },
      fieldName: 'email',
      label: '邮箱',
      rules: z.string().min(1, { message: '请输入邮箱' }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        placeholder: '密码',
      },
      fieldName: 'password',
      label: '密码'
    }
  ];
});
</script>

<template>
  <div class="w-[100%]">
    <AuthenticationLogin ref="loginInfoRef" v-if="step === 1" :form-schema="formSchema" :loading="loginLoading"
      @submit="validateInfo" />
    <TenantSelect v-if="step === 2" :loading="confirmLoading" @doLogin="doLogin" @back="back" :options="tenantList" />
  </div>
</template>
