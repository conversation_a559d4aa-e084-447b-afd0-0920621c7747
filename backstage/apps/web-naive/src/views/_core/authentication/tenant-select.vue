<script lang="ts" setup>
import { defineProps, ref, defineEmits } from 'vue';
import { useAccessStore } from '@vben/stores'
import { NForm, NFormItem, NSelect, NButton, NCard, type SelectOption } from 'naive-ui'
import { getUserInfoApi } from '#/api';
const accessStore = useAccessStore()
const props = defineProps({
  options: {
    type: Array<SelectOption>,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
})
const tenantId = ref(props?.options?.[0]?.value || '')
const emit = defineEmits(['doLogin', 'back'])
const goLogin = async () => {
  emit('doLogin', { tenantId: tenantId.value })
}
const goBack = () => {
  emit('back')
}
</script>

<template>
  <n-card class="bg-transparent" title="请选择您的租户" :bordered="false"
    :content-style="{ display: 'flex', flexDirection: 'column', padding: '0' }" :header-style="{ padding: 0 }">
    <n-form>
      <n-form-item>
        <n-select :default-value="options?.[0]?.value" placeholder="请选择登录租户" class="w-[100%]" v-model:value="tenantId"
          :options="props.options" />
      </n-form-item>
    </n-form>
    <n-button class="mb-3" type="primary" :loading="loading" @click="goLogin">确定</n-button>
    <n-button @click="goBack">返回</n-button>
  </n-card>
</template>
