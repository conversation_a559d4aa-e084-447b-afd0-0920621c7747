<template>
  <n-flex :wrap="false" class="w-[100%]" justify="space-between">
    <n-flex :wrap="false">
      <n-flex :wrap="false" align="center" :size="0">
        <span class="text-nowrap text-[#333639] opacity-60">
          选择日期
        </span>
        <n-date-picker v-model:value="timeRange" :on-confirm="fetchData" class="ml-2" :style="{ minWidth: '250px' }"
          type="daterange" />
        <span class="text-nowrap text-[#333639] opacity-60 ml-2">
          选择部门
        </span>
        <n-tree-select :style="{ minWidth: '150px' }" :options="tree" class="ml-2" :default-expanded-keys="defaultExpandKeys"
          v-model:value="departmentId" :on-update:expanded-keys="onExpand" key-field="departmentId" label-field="name"
          @update:value="() => fetchData(timeRange)" />
      </n-flex>
    </n-flex>
    <n-flex align="center" class="min-w-[150px] text-[#333639] opacity-50" :size="0">
      <n-icon class="mr-1">
        <MdiInfo />
      </n-icon>数据每30分钟更新
    </n-flex>
  </n-flex>
</template>

<script setup lang="ts">
import { ref, defineEmits, watchEffect } from 'vue'
import dayjs from 'dayjs'
import { NDatePicker, NFlex, NIcon, NTreeSelect, type TreeOption } from 'naive-ui'
import { MdiInfo } from '@vben/icons'
import { onMounted } from 'vue';
import { getDepartmentTreeApi } from '#/api';
import type { Value } from 'naive-ui/es/date-picker/src/interface';
import { type TreeNode, transListToTree, findAndModifyNodes } from '#/views/org-structure/utils/tree';
import type { Key } from 'naive-ui/es/tree/src/interface';
const getFirstDayOfYearTimestamp = () => {
  const currentYear = new Date().getFullYear();
  const firstDayOfYear = new Date(currentYear, 0, 1);
  return firstDayOfYear.getTime();
};

// 获取当前天0:0:0的时间戳
const getCurrentDayStartTimestamp = () => {
  const currentDate = new Date();
  currentDate.setHours(0, 0, 0, 0);
  return currentDate.getTime();
};
const emits = defineEmits(['fetchData'])
const defaultStartDate = getFirstDayOfYearTimestamp()
const defaultEndDate = getCurrentDayStartTimestamp()
const timeRange = ref<Value | undefined | null>([defaultStartDate, defaultEndDate])
const tree = ref<TreeNode[]>([])
const departmentId = ref<string>('')
const defaultExpandKeys = ref<Key[] | undefined>(undefined)
const fetchTree = async (depId?: string) => {
  const list = await getDepartmentTreeApi(depId)
  departmentId.value = list[0].departmentId
  tree.value = transListToTree(list.map((node: TreeNode) => {
    return {
      ...node,
    }
  })) || []
}

watchEffect(() => {
  if (defaultExpandKeys.value === undefined && tree?.value.length > 0) {
    defaultExpandKeys.value = [tree.value.find(node => node.hierarchyVO.level === 0)?.departmentId || '']
  }
})

const onExpand = async (_: string[], _options: Array<TreeOption | null>, meta: {
  node: TreeOption | null
  action: 'expand' | 'collapse' | 'filter'
}) => {
  if (meta.action === 'collapse') return
  if (meta?.node?.children?.length) {
    return
  }
  const newChildren = await getDepartmentTreeApi(meta?.node?.departmentId as string)
  const path = newChildren[0].hierarchyVO.ancestors
  const newTree = [...tree.value]
  findAndModifyNodes(newTree, path, 0, newChildren.map((node: TreeNode) => {
    return {
      ...node,
    }
  }))
  tree.value = newTree
}

const fetchData = async (v: number | (string | number | Date | dayjs.Dayjs | null | undefined)[] | null | undefined) => {
  const startDate = dayjs(v[0]).format('YYYYMMDD')
  const endDate = dayjs(v[1]).format('YYYYMMDD')
  emits('fetchData', { startDate, endDate, departmentId: departmentId.value })
}

onMounted(async () => {
  await fetchTree()
  await fetchData(timeRange.value)
})

</script>
