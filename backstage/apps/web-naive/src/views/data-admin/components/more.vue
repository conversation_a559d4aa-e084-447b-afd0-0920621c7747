<template>
  <n-card title="更多" class="mt-3">
    <n-flex :wrap="false">
      <n-card class="flex-1">
        <n-flex align="start" :vertical="true">
          <n-flex align="center">
            索引构建量
          </n-flex>
          <div class="card_number"><n-number-animation ref="numberAnimationInstRef" :from="0" :to="IndexConstructNumber"
              show-separator /></div>
        </n-flex>
      </n-card>
      <n-card class="flex-1">
        <n-flex align="start" :vertical="true">
          <n-flex align="center">
            索引构建成功量
          </n-flex>
          <div class="card_number"><n-number-animation ref="numberAnimationInstRef" :from="0"
              :to="IndexConstructSuccessNumber" show-separator /></div>
        </n-flex>
      </n-card>
      <n-card class="flex-1">
        <n-flex align="start" :vertical="true">
          <n-flex align="center">
            索引构建成功率（次数）
          </n-flex>
          <div class="card_number">
            <n-number-animation ref="numberAnimationInstRef" :from="0" :to="IndexConstructSuccessRate" show-separator />
            <span v-if="IndexConstructSuccessRate">%</span>
          </div>
        </n-flex>
      </n-card>
    </n-flex>
  </n-card>
</template>
<script setup lang="ts">
import { NCard, NFlex, NNumberAnimation } from 'naive-ui'
import { defineProps, computed } from 'vue'
import type { RateDataItem } from '../const'
import { rateCaltor } from '../util'

const props = defineProps({
  codeIndexRate: {
    type: Array<RateDataItem>,
    default: () => []
  }
})

const IndexConstructNumber = computed(() => {
  return props.codeIndexRate.find(item => item[0] === 'IndexConstruct')?.[1] || 0
});
const IndexConstructSuccessNumber = computed(() => {
  return props.codeIndexRate.find(item => item[0] === 'IndexConstructSuccess')?.[1] || 0
});
// 索引构建成功率 = 索引构建成功量 / 索引构建量
const IndexConstructSuccessRate = computed(() => {
  return IndexConstructNumber.value > 0 ?
    rateCaltor(IndexConstructSuccessNumber.value, IndexConstructNumber.value) : 0
});
</script>
