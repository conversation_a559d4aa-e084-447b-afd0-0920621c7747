import { roundToTwoDecimals } from '#/utils/math'
import type { RateDataItem } from './const'

export const handleRawString = (input: string, type?: string) => {
  const regex = /\('(\d{4}-\d{2}-\d{2})', (?:Decimal\('(\d+\.?\d*)'\)|(\d+\.?\d*))\)/g;
  const result: RateDataItem[] = [];
  let match;
  while ((match = regex.exec(input)) !== null) {
    const date = match[1];
    if (!date) {
      continue
    }
    let rate = 1
    if (type === 'completionRate') {
      rate = 100
    }
    let decimal = 100
    if (type === 'saveDevelopTime') {
      decimal = 10000
    }
    const number = roundToTwoDecimals(parseFloat(match[2] || match[3] || '0') * rate, decimal);
    result.push([date, number]);
  }
  return result
}

export function handleRateData(rate: string) {
  return JSON.parse(rate.replace(/'/g, '"').replace(/\(/g, '[').replace(/\)/g, ']'))
}

/**
* 不区分大小写，key 相同的 value 相加
* @param data
*/
export function mergeDuplicateNames(data: { name: string; value: number }[]) {
  const mergedMap: Record<string, number> = {};
  for (const item of data) {
    const normalizedName = item.name.toLowerCase();
    mergedMap[normalizedName] = (mergedMap[normalizedName] || 0) + item.value;
  }
  return Object.entries(mergedMap).map(([name, value]) => ({
    name,
    value,
  }));
}

export const rateCaltor = (num: number, total: number, decimal: number = 100) => {
  const result = roundToTwoDecimals((num / total) * 100, decimal)
  return result
}

/**
 * 从第10个数据开始，将后面数据进行累加，得到‘其他’作为一个数据
 * @param data
 */
export function mergeOtherLangs(data: { name: string; value: number }[]) {
  let result = data.slice(0, 9);
  let other = data.slice(9);
  const otherValue = other.reduce((acc, cur) => acc + cur.value, 0);
  result.push({
    name: '其他',
    value: otherValue
  })
  return result;
}
