export type RateDataItem = [string, number];

export const DEFAULT_COLOR = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#909090']

const commonEchatsOptions = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999'
      }
    }
  },
  toolbox: {
    feature: {
      saveAsImage: { show: true }
    }
  },
}

export const defaultUserTrendOption = {
  ...commonEchatsOptions,
  legend: {
    data: ['使用人数']
  },
  xAxis: [
    {
      type: 'category',
      data: [] as string[],
      axisPointer: {
        type: 'shadow'
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      name: '人数',
      axisLabel: {
        formatter: '{value}'
      }
    }
  ],
  series: [
    {
      name: '使用人数',
      type: 'line',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value as number) + '人';
        }
      },
      data: [] as any[],
    }
  ]
};

export const defaultTimeTrendOption = {
  ...commonEchatsOptions,
  legend: {
    data: ['节约开发时间']
  },
  xAxis: [
    {
      type: 'category',
      data: [] as string[],
      axisPointer: {
        type: 'shadow'
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      name: '时间(小时)',
      axisLabel: {
        formatter: '{value}'
      }
    }
  ],
  series: [
    {
      name: '节约开发时间',
      type: 'line',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value as number) + '小时';
        }
      },
      data: [] as any[]
    }
  ]
};

export const defaultCodeTrendOption = {
  ...commonEchatsOptions,
  legend: {
    data: ['代码采纳率']
  },
  xAxis: [
    {
      type: 'category',
      data: [] as string[],
      axisPointer: {
        type: 'shadow'
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      name: '比率(%)',
      axisLabel: {
        formatter: '{value}'
      }
    }
  ],
  series: [
    {
      name: '代码采纳率',
      type: 'line',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value as number) + ' %';
        }
      },
      data: [] as any[]
    }
  ]
};

export const defaultAcceptLanguageOption = {
  title: {
    text: '语言占比',
    left: 'center'
  },
  color: DEFAULT_COLOR,
  tooltip: {
    trigger: 'item',
    formatter: function (params: {
      data: any; name: any; value: any; percent: any;
    }) {
      return `${params.data.name}: ${params.data.value} (${params.percent}%)`
    }
  },
  toolbox: {
    feature: {
      saveAsImage: { show: true }
    },

  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '语言占比',
      type: 'pie',
      radius: '50%',
      top: '25px',
      data: [] as any[],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
};

export const defaultAskUserTrendOption = {
  ...commonEchatsOptions,
  legend: {
    data: ['使用人数']
  },
  xAxis: [
    {
      type: 'category',
      data: [] as string[],
      axisPointer: {
        type: 'shadow'
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      name: '人数',
      axisLabel: {
        formatter: '{value}'
      }
    }
  ],
  series: [
    {
      name: '使用人数',
      type: 'line',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value as number) + '人';
        }
      },
      data: []
    }
  ]
};

export const defaultAskAcceptRateTrendOption = {
  ...commonEchatsOptions,
  legend: {
    data: ['采纳率趋势']
  },
  xAxis: [
    {
      type: 'category',
      data: [] as string[],
      axisPointer: {
        type: 'shadow'
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      name: '比率(%)',
      axisLabel: {
        formatter: '{value}'
      }
    }
  ],
  series: [
    {
      name: '采纳率趋势',
      type: 'line',
      tooltip: {
        valueFormatter: function (value: number) {
          return (value as number) + '%';
        }
      },
      data: [] as any[],
    }
  ]
};

export const defaultAskLanguageRateOption = {
  title: {
    text: '语言占比',
    left: 'center',
  },
  color: DEFAULT_COLOR,
  tooltip: {
    trigger: 'item',
    formatter: function (params: {
      data: any; name: any; value: any; percent: any;
    }) {
      return `${params.data.name}: ${params.data.value} (${params.percent}%)`
    }
  },
  toolbox: {
    feature: {
      saveAsImage: { show: true }
    }
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '语言占比',
      type: 'pie',
      top: '25px',
      radius: '50%',
      data: [] as any[],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
};

export const defaultCommandAskRateOption = {
  title: {
    text: '指令使用分布',
    subtext: '仅 2025-04-01 之后数据',
    left: 'center',
  },
  color: DEFAULT_COLOR,
  tooltip: {
    trigger: 'item',
    formatter: function (params: {
      color: any;
      data: any; name: any; value: any; percent: any;
    }) {
      return `${params.data.name}: ${params.data.value} (${params.percent}%)`
    }
  },
  toolbox: {
    feature: {
      saveAsImage: { show: true }
    }
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '指令分布',
      type: 'pie',
      radius: '50%',
      top: '25px',
      data: [],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
};

export const defaultPluginAskRateOption = {
  title: {
    text: '插件使用分布',
    subtext: '仅 2025-04-01 之后数据',
    left: 'center',
  },
  color: DEFAULT_COLOR,
  tooltip: {
    trigger: 'item',
    formatter: function (params: {
      color: any;
      data: any; name: any; value: any; percent: any;
    }) {
      return `${params.data.name}: ${params.data.value} (${params.percent}%)`
    }
  },
  toolbox: {
    feature: {
      saveAsImage: { show: true }
    }
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '插件使用分布',
      type: 'pie',
      radius: '50%',
      top: '25px',
      data: [],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
};

export const defaultKnowledgeAskRateOption = {
  title: {
    text: '工程问答分布',
    subtext: '仅 2025-04-01 之后数据',
    left: 'center',
  },
  color: DEFAULT_COLOR,
  tooltip: {
    trigger: 'item',
    formatter: function (params: {
      color: any;
      data: any; name: any; value: any; percent: any;
    }) {
      return `${params.data.name}: ${params.data.value} (${params.percent}%)`
    }
  },
  toolbox: {
    feature: {
      saveAsImage: { show: true }
    }
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '工程问答分布',
      type: 'pie',
      radius: '50%',
      top: '25px',
      data: [],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
};

export const defaultCodeReviewRateOption = {
  title: {
    text: '代码评审分布',
    subtext: '仅 2025-04-01 之后数据',
    left: 'center',
  },
  color: DEFAULT_COLOR,
  tooltip: {
    trigger: 'item',
    formatter: function (params: {
      color: any;
      data: any; name: any; value: any; percent: any;
    }) {
      return `${params.data.name}: ${params.data.value} (${params.percent}%)`
    }
  },
  toolbox: {
    feature: {
      saveAsImage: { show: true }
    }
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '代码评审分布',
      type: 'pie',
      radius: '50%',
      top: '25px',
      data: [] as Array<{ name: string; value: number }>,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    },
  ]
};

export const CODE_REVIEW_RATE_MAP: Record<string, string> = {
  'file': '指定文件',
  'changed': '编辑区',
  'staged': '变更区',
}
