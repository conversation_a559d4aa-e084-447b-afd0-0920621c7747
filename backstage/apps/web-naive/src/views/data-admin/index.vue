<template>
  <div id="dataLoadingSpin" class="pt-3 px-3" :style="{ background: '#fff', height: '100%' }">
    <DateSelect @fetchData="(searchData) => fetchData(searchData)" />
    <n-spin :show="loading">
      <n-card title="概览" class="mt-3">
        <div class="pt-3">
          <n-flex :wrap="false">
            <n-card class="flex-1">
              <n-flex align="start" :vertical="true">
                <n-flex align="center">
                  新增用户数
                </n-flex>
                <div class="card_number">
                  <n-number-animation ref="numberAnimationInstRef" :from="0" :to="Number(info.new_accounts)"
                    show-separator />
                </div>
              </n-flex>
            </n-card>
            <n-card class="flex-1">
              <n-flex align="start" :vertical="true">
                <n-flex align="center">
                  活跃用户数
                </n-flex>
                <div class="card_number"><n-number-animation ref="numberAnimationInstRef" :from="0"
                    :to="Number(info.active_accounts)" show-separator /></div>
              </n-flex>
            </n-card>
            <n-card class="flex-1">
              <n-flex align="start" :vertical="true">
                <n-flex align="center">
                  累计用户数
                </n-flex>
                <div class="card_number"><n-number-animation ref="numberAnimationInstRef" :from="0"
                    :to="Number(info.total_accounts)" show-separator /></div>
              </n-flex>
            </n-card>
            <n-card class="flex-1">
              <n-flex align="start" :vertical="true">
                <n-flex align="center">
                  Token使用量
                </n-flex>
                <div class="card_number"><n-number-animation ref="numberAnimationInstRef" :from="0"
                    :to="Number(info.tokens)" show-separator /></div>
              </n-flex>
            </n-card>
          </n-flex>
        </div>
      </n-card>
      <n-card title="代码补全" class="mt-3">
        <div class="pt-3">
          <n-flex :wrap="false">
            <n-card class="flex-1">
              <n-flex align="start" :vertical="true">
                <n-flex align="center">
                  代码补全触发次数
                </n-flex>
                <div class="card_number">
                  <n-number-animation ref="numberAnimationInstRef" :from="0" :to="Number(info.completion_codes)"
                    show-separator />
                </div>
              </n-flex>
            </n-card>
            <n-card class="flex-1">
              <n-flex align="start" :vertical="true">
                <n-flex align="center">
                  代码补全成功次数
                </n-flex>
                <div class="card_number"><n-number-animation ref="numberAnimationInstRef" :from="0"
                    :to="Number(info.completion_success_codes)" show-separator /></div>
              </n-flex>
            </n-card>
            <n-card class="flex-1">
              <n-flex align="start" :vertical="true">
                <n-flex align="center">
                  代码补全行数
                </n-flex>
                <div class="card_number"><n-number-animation ref="numberAnimationInstRef" :from="0"
                    :to="Number(info.completion_success_lines)" show-separator /></div>
              </n-flex>
            </n-card>

          </n-flex>
          <n-flex class="mt-3" :wrap="false">
            <n-card class="flex-1">
              <n-flex align="start" :vertical="true">
                <n-flex align="center">
                  代码补全采纳率
                </n-flex>
                <div class="card_number"><n-number-animation ref="numberAnimationInstRef" :from="0"
                    :to="Number(info.completion_success_codes) ? rateCaltor(Number(info.completion_accept_codes), Number(info.completion_success_codes), 100) : 0"
                    show-separator />
                  <span v-if="info.completion_success_codes">%</span>
                </div>
              </n-flex>
            </n-card>
            <n-card class="flex-1">
              <n-flex align="start" :vertical="true">
                <n-flex align="center">
                  代码补全采纳次数
                </n-flex>
                <div class="card_number"><n-number-animation ref="numberAnimationInstRef" :from="0"
                    :to="Number(info.completion_accept_codes)" show-separator /></div>
              </n-flex>
            </n-card>
            <n-card class="flex-1">
              <n-flex align="start" :vertical="true">
                <n-flex align="center">
                  代码补全采纳行数
                </n-flex>
                <div class="card_number"><n-number-animation ref="numberAnimationInstRef" :from="0"
                    :to="Number(info.completion_accept_lines)" show-separator /></div>
              </n-flex>
            </n-card>
          </n-flex>
          <n-divider />
          <n-flex class="mt-3" :wrap="false">
            <EchartsUI ref="userTrendRef" />
            <EchartsUI ref="timeTrendRef" />
          </n-flex>
          <n-divider />
          <n-flex class="mt-3" :wrap="false">
            <EchartsUI ref="codeTrendRef" />
            <EchartsUI ref="acceptLanguageRef" :style="{ height: '400px' }" />
          </n-flex>
        </div>
      </n-card>
      <n-card title="智能问答" class="mt-3">
        <n-flex :wrap="false">
          <n-card class="flex-1">
            <n-flex align="start" :vertical="true">
              <n-flex align="center">
                指令提问量/提问量
              </n-flex>
              <div class="card_number"><n-number-animation ref="numberAnimationInstRef" :from="0"
                  :to="Number(info.command_asks)" show-separator />/<n-number-animation ref="numberAnimationInstRef"
                  :from="0" :to="Number(info.asks)" show-separator /></div>

            </n-flex>
          </n-card>
          <n-card class="flex-1">
            <n-flex align="start" :vertical="true">
              <n-flex align="center">
                代码生成次数
              </n-flex>
              <div class="card_number"><n-number-animation ref="numberAnimationInstRef" :from="0"
                  :to="Number(info.generate_code)" show-separator /></div>
            </n-flex>
          </n-card>
          <n-card class="flex-1">
            <n-flex align="start" :vertical="true">
              <n-flex align="center">
                代码生成行数
              </n-flex>
              <div class="card_number"><n-number-animation ref="numberAnimationInstRef" :from="0"
                  :to="Number(info.generate_code_lines)" show-separator /></div>
            </n-flex>
          </n-card>
        </n-flex>

        <n-flex class="mt-3" :wrap="false">
          <n-card class="flex-1">
            <n-flex align="start" :vertical="true">
              <n-flex align="center">
                代码采纳率（次数）
              </n-flex>
              <div class="card_number">
                <n-number-animation ref="numberAnimationInstRef" :from="0"
                  :to="Number(info.generate_code) ? rateCaltor(Number(info.accept_codes), Number(info.generate_code), 100) : 0"
                  show-separator />
                <span v-if="info.generate_code">%</span>
              </div>
            </n-flex>
          </n-card>
          <n-card class="flex-1">
            <n-flex align="start" :vertical="true">
              <n-flex align="center">
                代码采纳次数
              </n-flex>
              <div class="card_number"><n-number-animation ref="numberAnimationInstRef" :from="0"
                  :to="Number(info.accept_codes)" show-separator /></div>
            </n-flex>
          </n-card>
          <n-card class="flex-1">
            <n-flex align="start" :vertical="true">
              <n-flex align="center">
                代码采纳行数
              </n-flex>
              <div class="card_number"><n-number-animation ref="numberAnimationInstRef" :from="0"
                  :to="Number(info.accept_line_codes)" show-separator /></div>
            </n-flex>
          </n-card>
        </n-flex>
        <n-divider />
        <n-flex class="mt-3" :wrap="false">
          <EchartsUI ref="askUserTrendRef" />
          <EchartsUI ref="askAcceptRateTrendRef" />
        </n-flex>
        <n-divider />
        <n-flex class="mt-3" :wrap="false">
          <EchartsUI ref="askLanguageRateRef" :style="{ height: '400px' }" />
          <EchartsUI ref="commandAskRateRef" :style="{ height: '400px' }" />
        </n-flex>
        <n-flex class="mt-3" :wrap="false">
          <EchartsUI ref="pluginAskRateRef" :style="{ height: '400px' }" />
          <EchartsUI ref="knowledgeAskRateRef" :style="{ height: '400px' }" />
        </n-flex>
        <n-flex class="mt-3" :wrap="false">
          <EchartsUI ref="codeReviewRateRef" :style="{ height: '400px', width: '50%' }" />
        </n-flex>
      </n-card>

      <More :codeIndexRate="info.codeIndexRate" />
    </n-spin>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { NCard, NFlex, NSpin, NNumberAnimation, NDivider, NIcon } from 'naive-ui'
import {
  EchartsUI,
  type EchartsUIType,
  useEcharts,
} from '@vben/plugins/echarts';
import 'echarts/lib/component/dataZoom';
import { getDepartmentDashboardApi } from '#/api'
import type { EChartsOption } from 'echarts';
import DateSelect from './components/date-select.vue'
import { convertNoneToZero } from '#/utils/transformer';
import { ASK_PIE_TEXT, COMMAND_LIST, KNOWLEDGE_LIST, PLUGIN_LIST } from '#/constant/ask-pie';
import type { RateDataItem } from './const'
import { defaultUserTrendOption, CODE_REVIEW_RATE_MAP, defaultCodeReviewRateOption, defaultKnowledgeAskRateOption, defaultPluginAskRateOption, defaultTimeTrendOption, defaultCodeTrendOption, defaultAcceptLanguageOption, defaultAskUserTrendOption, defaultAskAcceptRateTrendOption, defaultAskLanguageRateOption, defaultCommandAskRateOption } from './const'
import { handleRawString, handleRateData, mergeDuplicateNames, rateCaltor, mergeOtherLangs } from './util'
import { cloneDeep } from 'lodash'
import More from './components/more.vue'
import { MdiInfo } from '@vben/icons'

const loading = ref(false)
const info = ref<{ [x: string]: any; useTrend: RateDataItem[], saveDevelopTime: RateDataItem[], completionRate: RateDataItem[], codeReviewRate: RateDataItem[], codeIndexRate: RateDataItem[] }>({
  new_accounts: "0",
  active_accounts: "0",
  total_accounts: "0",
  conversations: "0",
  total_conversations: "0",
  tokens: "0",
  asks: "0",
  command_asks: "0",
  generate_code_lines: "0",
  code_shows: "0",
  accept_codes: "0",
  completion_codes: "0",
  completion_success_codes: "0",
  completion_accept_codes: "0",
  completion_accept_lines: "0",
  completion_accept_characters: "0",
  useTrend: [],
  saveDevelopTime: [],
  completionRate: [],
  codeReviewRate: [],
  codeIndexRate: [],
})

const userTrendRef = ref<EchartsUIType>();
const timeTrendRef = ref<EchartsUIType>();
const codeTrendRef = ref<EchartsUIType>();
const acceptLanguageRef = ref<EchartsUIType>();
const askUserTrendRef = ref<EchartsUIType>();
const askAcceptRateTrendRef = ref<EchartsUIType>();
const askLanguageRateRef = ref<EchartsUIType>();
const commandAskRateRef = ref<EchartsUIType>();
const pluginAskRateRef = ref<EchartsUIType>();
const knowledgeAskRateRef = ref<EchartsUIType>();
const codeReviewRateRef = ref<EchartsUIType>();

const { renderEcharts: userTrendRenderEcharts } = useEcharts(userTrendRef);
const { renderEcharts: timeTrendRenderEcharts } = useEcharts(timeTrendRef);
const { renderEcharts: codeTrendRenderEcharts } = useEcharts(codeTrendRef)
const { renderEcharts: acceptLanguageRenderEcharts } = useEcharts(acceptLanguageRef)
const { renderEcharts: askUserTrendRenderEcharts } = useEcharts(askUserTrendRef);
const { renderEcharts: askAcceptRateTrendRenderEcharts } = useEcharts(askAcceptRateTrendRef);
const { renderEcharts: askLanguageRateRenderEcharts } = useEcharts(askLanguageRateRef)
const { renderEcharts: commandAskRateRenderEcharts } = useEcharts(commandAskRateRef)
const { renderEcharts: pluginAskRateRenderEcharts } = useEcharts(pluginAskRateRef)
const { renderEcharts: knowledgeAskRateRenderEcharts } = useEcharts(knowledgeAskRateRef)
const { renderEcharts: codeReviewRateRenderEcharts } = useEcharts(codeReviewRateRef)

const userTrendOption = cloneDeep(defaultUserTrendOption);
const timeTrendOption = cloneDeep(defaultTimeTrendOption);
const codeTrendOption = cloneDeep(defaultCodeTrendOption);
const acceptLanguageOption = cloneDeep(defaultAcceptLanguageOption);
const askUserTrendOption = cloneDeep(defaultAskUserTrendOption);
const askAcceptRateTrendOption = cloneDeep(defaultAskAcceptRateTrendOption);
const askLanguageRateOption = cloneDeep(defaultAskLanguageRateOption);
const commandAskRateOption = cloneDeep(defaultCommandAskRateOption);
const pluginAskRateOption = cloneDeep(defaultPluginAskRateOption);
const knowledgeAskRateOption = cloneDeep(defaultKnowledgeAskRateOption);
const codeReviewRateOption = cloneDeep(defaultCodeReviewRateOption);

const renderEcharts = () => {
  userTrendRenderEcharts(userTrendOption as EChartsOption)
  timeTrendRenderEcharts(timeTrendOption as EChartsOption)
  codeTrendRenderEcharts(codeTrendOption as EChartsOption)
  acceptLanguageRenderEcharts(acceptLanguageOption as EChartsOption)
  askUserTrendRenderEcharts(askUserTrendOption as EChartsOption)
  askAcceptRateTrendRenderEcharts(askAcceptRateTrendOption as EChartsOption)
  askLanguageRateRenderEcharts(askLanguageRateOption as EChartsOption)
  commandAskRateRenderEcharts(commandAskRateOption as EChartsOption)
  pluginAskRateRenderEcharts(pluginAskRateOption as EChartsOption)
  knowledgeAskRateRenderEcharts(knowledgeAskRateOption as EChartsOption)
  codeReviewRateRenderEcharts(codeReviewRateOption as EChartsOption)
};

onMounted(() => {
  // plugins中的echarts未导出该类型
  renderEcharts();
})
const fetchData = async (searchData: { startDate: string, endDate: string, departmentId: string }) => {
  loading.value = true
  const res = await getDepartmentDashboardApi({
    ...searchData,
  })
  const { completion_user_trend, save_develop_time, completion_accept_rate, completion_accept_language,
    ask_accept_rate_trend, ask_user_trend, ask_language_rate, command_ask_rate, code_review_rate, code_index_rate } = res

  const useTrend = handleRawString(completion_user_trend)
  const saveDevelopTime = handleRawString(save_develop_time, 'saveDevelopTime')
  const completionRate = handleRawString(completion_accept_rate, 'completionRate')
  const acceptLanguage = handleRateData(completion_accept_language)
  const askUserTrend = handleRawString(ask_user_trend)
  const askAcceptRateTrend = handleRawString(ask_accept_rate_trend, 'askAcceptRateTrend')
  const askLanguageRate = handleRateData(ask_language_rate)
  const commandAskRate = handleRateData(command_ask_rate)
  const codeReviewRate = handleRateData(code_review_rate)
  const codeIndexRate = handleRateData(code_index_rate)

  const data = convertNoneToZero(res)
  loading.value = false

  info.value = { ...data, useTrend, saveDevelopTime, completionRate, acceptLanguage, askUserTrend, askAcceptRateTrend, askLanguageRate, commandAskRate, codeReviewRate, codeIndexRate }
}

const updateEchartsOption = () => {
  // 更新 ECharts 选项中的数据
  if (userTrendOption?.xAxis?.[0]?.data && userTrendOption?.series?.[0]?.data) {
    userTrendOption.xAxis[0].data = info.value.useTrend.map(item => item[0]);
    userTrendOption.series[0].data = info.value.useTrend.map(item => item[1]);
  }
  if (timeTrendOption?.xAxis?.[0]?.data && timeTrendOption?.series?.[0]?.data) {
    timeTrendOption.xAxis[0].data = info.value.saveDevelopTime.map(item => item[0]);
    timeTrendOption.series[0].data = info.value.saveDevelopTime.map(item => Number((Number(item[1]) * 24).toFixed(2)));
  }
  if (codeTrendOption?.xAxis?.[0]?.data && codeTrendOption?.series?.[0]?.data) {
    codeTrendOption.xAxis[0].data = info.value.completionRate.map(item => item[0]);
    codeTrendOption.series[0].data = info.value.completionRate.map(item => Number(item[1]).toFixed(2));
  }
  if (acceptLanguageOption?.series?.[0]?.data) {
    let result = mergeDuplicateNames(info.value.acceptLanguage.map((item: any) => ({
      name: item[0] || '其他',
      value: item[1]
    }))).sort((a: { value: number; }, b: { value: number; }) => b.value - a.value)
    if (result.length > 9) {
      // 合并
      result = mergeOtherLangs(result);
    }
    acceptLanguageOption.series[0].data = result;
  }

  if (askUserTrendOption?.xAxis?.[0]?.data && askUserTrendOption?.series?.[0]?.data) {
    askUserTrendOption.xAxis[0].data = info.value.askUserTrend.map((item: any[]) => item[0]);
    askUserTrendOption.series[0].data = info.value.askUserTrend.map((item: any[]) => item[1]);
  }

  if (askAcceptRateTrendOption?.xAxis?.[0]?.data && askAcceptRateTrendOption?.series?.[0]?.data) {
    askAcceptRateTrendOption.xAxis[0].data = info.value.askAcceptRateTrend.map((item: any[]) => item[0]);
    askAcceptRateTrendOption.series[0].data = info.value.askAcceptRateTrend.map((item: any[]) => (item[1] * 100).toFixed(2));
  }

  if (askLanguageRateOption?.series?.[0]?.data) {
    let result = mergeDuplicateNames(info.value.askLanguageRate.map((item: any) => ({
      name: item[0] || '其他',
      value: item[1]
    }))).sort((a: { value: number; }, b: { value: number; }) => b.value - a.value)
    if (result.length > 9) {
      // 合并
      result = mergeOtherLangs(result);
    }
    askLanguageRateOption.series[0].data = result;
  }
  const data = info.value.commandAskRate.map((item: any) => ({
    name: ASK_PIE_TEXT[item[0]] || '其他',
    value: item[1]
  })).sort((a: { value: number; }, b: { value: number; }) => b.value - a.value);

  if (commandAskRateOption?.series?.[0]?.data) {
    commandAskRateOption.series[0].data = data.filter(((it: { name: string; }) => {
      return COMMAND_LIST.includes(it.name)
    }))
  }

  if (pluginAskRateOption?.series?.[0]?.data) {
    pluginAskRateOption.series[0].data = data.filter(((it: { name: string; }) => {
      return PLUGIN_LIST.includes(it.name)
    }))
  }

  if (knowledgeAskRateOption?.series?.[0]?.data) {
    knowledgeAskRateOption.series[0].data = data.filter(((it: { name: string; }) => {
      return KNOWLEDGE_LIST.includes(it.name)
    }))
  }

  if (codeReviewRateOption?.series?.[0]?.data) {
    codeReviewRateOption.series[0].data = info.value.codeReviewRate.map(item => ({
      name: CODE_REVIEW_RATE_MAP[item[0]] || '其他',
      value: item[1]
    }))
  }
}

watch(info, () => {
  updateEchartsOption()

  renderEcharts()
})
</script>

<style lang="scss">
.card_number {
  font-size: 24px;
  font-weight: bold;
  font-family: 'product sans';
}

#dataLoadingSpin {
  .n-spin-container .n-spin-body {
    top: 45vh !important;
  }
}
</style>
