<template>
  <SelectModal :show="props.show" :no-more="noMore" :data-list="preKnowledgeList" :loading="isLoading" modal-name="知识库"
    @on-cancel="emits('onCancel')" @on-ok="params => emits('onOk', params)" @load-data="loadData">
    <template #prompt-message>
      <p style="font-size: 12px;">若未找到知识库，可前往<n-button type="primary" text tag="a" target="_blank"
          :href="`${getKnowledgeBaseUrl()}/document/#/basicLayout/knowledgeBase`"
          style="font-size: 12px;">「知识库应用」</n-button>进行创建</p>
    </template>
  </SelectModal>
</template>
<script setup lang="ts">
import { ref, defineProps, defineEmits, watchEffect } from 'vue'
import SelectModal, { type DataItem } from '#/components/select-modal/index.vue'
import { queryKnowledgeListApi } from '#/api'
import { useAsyncState } from '@vueuse/core'
import { NButton } from 'naive-ui'
import { getKnowledgeBaseUrl } from '#/utils'

const props = defineProps({
  show: Boolean,
})
const emits = defineEmits(['onCancel', 'onOk'])

const preKnowledgeList = ref<Array<DataItem>>([])
const noMore = ref(false)

const { state: knowledgeList, execute, isLoading } = useAsyncState(async ({ key = undefined, currentPage = 1 }: { key?: string, currentPage: number }) => {
  const res = await queryKnowledgeListApi({
    pageSize: 20,
    pageNo: currentPage,
    name: key
  })

  if (!res) {
    return preKnowledgeList.value
  }

  if (res.total <= res.current * res.size) {
    noMore.value = true
  } else {
    noMore.value = false
  }

  const newData = res.records.map(i => ({
    id: i.id,
    name: i.name,
  }))

  if (currentPage === 1) {
    preKnowledgeList.value = newData;
  } else {
    preKnowledgeList.value.push(...newData)
  }

  return preKnowledgeList.value
}, [], {
  immediate: true,
  resetOnExecute: false
})

const loadData = (params: { key: string, current: number }) => {
  execute(1000, { key: params.key, currentPage: params.current })
}

watchEffect(() => {
  if (props.show) {
    preKnowledgeList.value = []
    execute(1000, {
      currentPage: 1
    })
  }
})
</script>
