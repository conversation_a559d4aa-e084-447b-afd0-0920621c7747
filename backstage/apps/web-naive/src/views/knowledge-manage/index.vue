<template>
  <div class="h-[100%] py-4 px-8">
    <n-flex justify="flex-end">
      <div>
        <n-input placeholder="请输入知识库名称" @update:value="() => handleSearch()" v-model:value="searchValue" clearable>
          <template #prefix>
            <n-icon>
              <MdiSearch />
            </n-icon>
          </template>
        </n-input>
      </div>

      <n-button @click="showModal = true" type="primary">
        <n-icon>
          <MdiPlus />
        </n-icon>
        添加知识库
      </n-button>
    </n-flex>

    <n-data-table class="pt-3" :columns="columns" :pagination="paginationReactive" :remote="true" :data="knowledgeList"
      :loading="isLoading" :bordered="false" />

    <SelectKnowledge :show="showModal" @on-cancel="() => showModal = false" @on-ok="onAddKnowledge" />
  </div>
</template>
<script setup lang="ts">
import { reactive, ref, h, onMounted } from 'vue'
import { NButton, NIcon, NDataTable, NDivider, NPopconfirm, NInput, NFlex } from 'naive-ui';
import { MdiPlus, MdiSearch } from '@vben/icons';
import { message } from '#/adapter/naive';
import SelectKnowledge from './select-knowledge.vue';
import { debounce } from 'lodash'
import { saveKnowledgesApi, getKnowledgePageApi, deleteKnowledgeApi, type KnowledgeApi } from '#/api'
import { useAsyncState } from '@vueuse/core'
import { getKnowledgeBaseUrl } from '#/utils'

interface SelectedKnowledgeItem {
  id: string;
  name: string;
}

// 添加知识库弹窗
const showModal = ref(false)
// 搜索名称
const searchValue = ref('')
const knowledgeBaseUrl = getKnowledgeBaseUrl()

// table columns
const columns = [
  {
    title: '知识库名称',
    key: 'name',
    ellipsis: true
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    render(row: KnowledgeApi.KnowledgePageItem) {
      return h('div', [
        h(NButton, {
          size: 'small',
          text: true,
          tag: 'a',
          href: `${knowledgeBaseUrl}/document/#/layoutWithoutMenu/innerKnowledgeBase/file?knowledgeBaseId=${row.knowledgeBaseId}`,
          target: '_blank'
        }, { default: () => '查看' }),

        h(NDivider, { vertical: true }),

        h(NPopconfirm, {
          onPositiveClick: async () => {
            await deleteKnowledgeApi(row.id);
            execute(1000)
            message.success('移除成功')
          },
        }, {
          default: () => `确定移除知识库「${row.name}」?`,
          trigger: () => h(NButton, {
            size: 'small',
            text: true,
            type: 'error',
            style: 'marginLeft: 0;'
          }, {
            default: () => '移除'
          })
        })
      ])
    }
  }
]
// 分页参数
const paginationReactive = reactive({
  page: 1,
  itemCount: 10,
  pageCount: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: async (page: number) => {
    paginationReactive.page = page
    execute(1000)
  },
  onUpdatePageSize: async (pageSize: number) => {
    paginationReactive.pageSize = pageSize
    paginationReactive.page = 1
    execute(1000)
  },
})

const { state: knowledgeList, execute, isLoading } = useAsyncState(async () => {
  const res = await getKnowledgePageApi({
    size: paginationReactive.pageSize,
    current: paginationReactive.page,
    name: searchValue.value
  })

  if (!res) {
    return []
  }

  paginationReactive.itemCount = res.total; // 总条数

  return res.records
}, [], {
  immediate: true,
  resetOnExecute: false
})

onMounted(() => {
  execute(1000)
})


const onAddKnowledge = async (selectedKnowledge: SelectedKnowledgeItem[]) => {
  if (selectedKnowledge?.length === 0) return;

  const data = selectedKnowledge.map(i => ({ name: i.name, knowledgeBaseId: i.id }))

  await saveKnowledgesApi(data)

  execute(1000)
  showModal.value = false;
}

const handleSearch = debounce(async () => {
  paginationReactive.page = 1;
  execute(1000)
}, 1000)

</script>
