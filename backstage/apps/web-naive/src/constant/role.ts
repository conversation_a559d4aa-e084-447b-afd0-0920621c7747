import type { SelectOption } from 'naive-ui'

export const PermissionArr = [
  {
    label: '租户管理员',
    value: 'TENANT_ADMIN'
  },
  {
    label: '超级管理员',
    value: 'SUPER_ADMIN'
  },
  {
    label: '普通用户',
    value: 'NORMAL'
  }
]

export const PermissionMap: { [key: string]: string } = {
  TENANT_ADMIN: '租户管理员',
  SUPER_ADMIN: '超级管理员',
  NORMAL: '普通用户'
}

export const RoleType = {
  TENANT_ADMIN: 'TENANT_ADMIN',
  SUPER_ADMIN: 'SUPER_ADMIN',
  NORMAL: 'NORMAL',
  NOONE: 'NOONE',
}

export const getRoleOptions = (role: string, isSelf: boolean = false) => {
  let options: SelectOption[] = PermissionArr
  if (role === RoleType.SUPER_ADMIN) {
    return {
      options,
      disabled: true
    }
  }
  let disabled = false
  if (role === RoleType.TENANT_ADMIN) {
    options = PermissionArr.filter((item) => {
      return item.value !== RoleType.SUPER_ADMIN
    })
  }
  return { options, disabled }
}
