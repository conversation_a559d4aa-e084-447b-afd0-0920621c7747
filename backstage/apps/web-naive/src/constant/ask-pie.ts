export const ASK_PIE_TEXT: {
  [key: string]: string
} = {
  'Install': '安装成功',
  'Invoke': '唤起插件',
  'Help': '点击帮助中心',
  'PluginSetting': '点击插件设置',
  'ClearSession': '点击清除会话',
  'DeleteSession': '删除会话',
  'LoadSession': '切换会话',
  'UpdateSessionTitle': '更新会话标题',
  'Ask': '点击发送提问',
  'AskFrontEnd': '点击发送提问',
  'StopAnswer': '点击停止应答',
  'AnswerShow': '应答展示',
  'CodeShow': '代码展示',
  'Copy': '点击复制',
  'Accept': '点击采纳',
  'NewFile': '点击新建文件',
  'CodeRecommend': '触发代码推荐',
  'AcceptCodeRecommend': '采纳推荐代码',
  'StartResponse': '开始应答',
  'AbortResponse': '中止响应',
  'CodeRecommendSuccess': '代码推荐成功',

  'AskBackEnd': '问答-后端',
  'CodeExplanation': '代码解释',
  'FunctionComments': '函数注释',
  'CodeCompletion': '代码补全',
  'CodeComments': '行间注释',
  'CodeOptimize': '代码调优',
  'FixCode': '',
  'GenerateUnitTest': '生成单元测试',
  'GenerateGitCommit': '生成git commit',
  'GenerateGitMsg': '生成git commit message',
  'GitIntroduce': 'Git介绍',
  'SecurityCheck': '预审查',
  'CodeTranslate': '代码翻译',

  'ToolboxIntroduce': '插件介绍',
  'Base64Encode': 'Base64编码',
  'Base64Decode': 'Base64解码',
  'MD5Encode': 'MD5加密',
  'URIDecode': 'URIDecode 解码',
  'URIEncode': 'URIEncode 编码',
  'JSONToYAML': 'JSON转YAML',
  'YAMLToJSON': 'YAML转JSON',
  'JSONToTypeScriptType': 'JSON转TS类型',
  'JSONToGolangType': 'JSON转Golang类型',
  'JSONToJavaClass': 'JSON转Java类',
  'JSONFormat': 'JSON格式化',
  'JSONCompress': 'JSON压缩',
  'JWTToJSON': 'JWT转JSON',

  // 工程问答 代码库
  'ProjectQARepository': '代码库知识',
  // 工程问答 目录
  'ProjectQADirectory': '目录知识',
  // 工程问答 文件
  'ProjectQAFile': '文件知识',

  'CodeReview': '代码评审',
}

export const COMMAND_LIST = [
  '代码解释',
  '函数注释',
  '行间注释',
  '代码调优',
  '生成单元测试',
  '代码翻译',
  '代码评审'
]

export const PLUGIN_LIST = [
  '插件介绍',
  'Base64编码',
  'Base64解码',
  'MD5加密',
  'URIDecode 解码',
  'URIEncode 编码',
  'JSON转YAML',
  'YAML转JSON',
  'JSON转TS类型',
  'JSON转Golang类型',
  'JSON转Java类',
  'JSON格式化',
  'JSON压缩',
  'JWT转JSON',
]

export const KNOWLEDGE_LIST = [
  '代码库知识',
  // 工程问答 目录
  '目录知识',
  // 工程问答 文件
  '文件知识',
]
