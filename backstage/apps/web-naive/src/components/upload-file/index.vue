<template>
  <n-upload :accept="props.accept" :max="props.max" :data="props.data" list-type="image-card" :custom-request="onUpload"
    :default-file-list="props.defaultFileList">
    点击上传
  </n-upload>
</template>
<script setup lang="ts">
import { defineEmits } from 'vue'
import { NUpload } from 'naive-ui'
import type { UploadCustomRequestOptions, UploadFileInfo } from 'naive-ui'
import { useMessage } from 'naive-ui'
import { uploadFileApi } from '#/api'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  },
  max: {
    type: Number,
    default: 1
  },
  accept: {
    type: String,
    default: 'image'
  },
  defaultFileList: {
    type: Array<UploadFileInfo>,
    default: () => []
  }
})
const emits = defineEmits(['onFinish'])
const message = useMessage()


const onUpload = ({
  file,
  data,
  headers,
  onFinish,
  onError,
  onProgress
}: UploadCustomRequestOptions) => {
  const formData = new FormData()
  if (data) {
    Object.keys(data).forEach((key) => {
      formData.append(
        key,
        data[key as keyof UploadCustomRequestOptions['data']]
      )
    })
  }
  formData.append('file', file.file as File)

  uploadFileApi(formData).then((res) => {
    emits('onFinish', res.data || '')
    if (!res?.data) {
      message.error(res?.message)
    }
  }).catch((err) => {
    emits('onFinish', '')
    message.error(err.message || '上传失败')
  })
}
</script>
