<template>
  <div class="flex-1" style="width: 100%;">
    <n-input v-model:value="searchkey" @update:value="(value: string) => onInput(value)" class="mb-2" enter-button
      clearable :placeholder="`搜索${props.modalName}`">
      <template #suffix>
        <n-icon>
          <MdiSearch />
        </n-icon>
      </template>
    </n-input>

    <n-infinite-scroll :style="`height: ${props.scrollHeight}px`" :distance="10" @load="handleLoad">
      <div v-for="item in props.dataList" :key="item.id">
        <n-checkbox :on-update:checked="(checked: boolean) => {
          if (checked) {
            if (!multiple) {
              emits('setSelectedList', [item])
            } else {
              emits('setSelectedList', [...selectedList, item])
            }
          } else {
            emits('setSelectedList', selectedList.filter(user => user.id !== item.id))
          }
        }" :checked="selectedList.some(i => i.id === item.id)" :value="item.id" :label="item.name" size="small"
          style="margin: 5px 0">
          <span v-if="item.telephone" style="color: rgba(0,0,0,.45); font-size: 12px">
            ({{ item.telephone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2') }})
          </span>
        </n-checkbox>
      </div>

      <p v-if="loading" class="text-center">
        加载中...
      </p>
      <p v-if="!loading && current > 1 && noMore" class="text-center">
        没有更多了
      </p>
    </n-infinite-scroll>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, defineProps, defineEmits, watchEffect } from 'vue'
import { NInput, NCheckbox, NIcon, NInfiniteScroll } from 'naive-ui'
import { MdiSearch } from '@vben/icons'
import { type DataItem } from './index.vue'

const props = defineProps({
  modalName: String,
  scrollHeight: {
    type: Number,
    default: 500
  },
  multiple: {
    type: Boolean,
    default: false
  },
  noMore: Boolean,
  loading: Boolean,
  selectedList: {
    type: Array<DataItem>,
    default: () => []
  },
  dataList: {
    type: Array<DataItem>,
    default: () => []
  }
})

const emits = defineEmits(['setSelectedList', 'loadData'])

const searchkey = ref<string>('')
const current = ref<number>(1)

const handleLoad = () => {
  if (props.loading || props.noMore)
    return
  current.value = current.value + 1;

  emits('loadData', { key: searchkey.value, current: current.value })
}

const onInput = (input: string) => {
  searchkey.value = input;
  current.value = 1;

  emits('loadData', { key: input, current: current.value })
}
</script>
