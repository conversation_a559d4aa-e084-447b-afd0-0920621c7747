<template>
  <n-modal :style="{ width: '650px' }" v-model:show="props.show" :title="`添加${props.modalName}`" preset="dialog"
    :show-icon="false" content-class="pt-1" :on-close="() => emits('onCancel')">
    <n-flex :wrap="false" class="h-[550px] pt-3">
      <n-flex class="flex-1" :vertical="true">
        <DataList :modal-name="props.modalName" :selectedList="selectedList" :dataList="props.dataList" :multiple="true"
          :noMore="props.noMore" :loading="props.loading" :scrollHeight="props.scrollHeight"
          @setSelectedList="onItemCheck" @loadData="params => emits('loadData', params)" />

        <slot name="prompt-message"></slot>
      </n-flex>

      <n-flex class="flex-1 bg-[#f8f9f9] px-2" vertical>
        <n-flex class="w-[100%] h-[34px]" justify="space-between" align="center">
          <span>已选择</span>
          <n-button size="small" text type="primary" @click="selectedList = []">清空</n-button>
        </n-flex>
        <div>
          <n-tag class="ml-1" round v-for="i in selectedList" closable @close="() => handleClose(i.id)"
            style="margin-bottom: 5px;">
            {{ i.name }}
          </n-tag>
        </div>
      </n-flex>
    </n-flex>

    <template #action>
      <div class="flex justify-end gap-2">
        <n-button @click="cancel">取消</n-button>
        <n-button type="primary" @click="confirm" :disabled="selectedList.length === 0">确定</n-button>
      </div>

    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue'
import { NModal, NFlex, NButton, NTag } from 'naive-ui'
import DataList from './data-list.vue'
export interface DataItem {
  id: string
  name: string
  telephone?: string
}
const props = defineProps({
  show: Boolean,
  modalName: String,
  noMore: Boolean,
  loading: Boolean,
  scrollHeight: {
    type: Number,
    default: 500
  },
  dataList: {
    type: Array<DataItem>,
    default: () => []
  }
})
const emits = defineEmits(['onCancel', 'onOk', 'loadData'])

const selectedList = ref<DataItem[]>([])

const handleClose = (id: string) => {
  selectedList.value = selectedList.value.filter((user: DataItem) => user.id !== id)
}

const onItemCheck = (list: DataItem[]) => {
  selectedList.value = list
}

const confirm = () => {
  emits('onOk', selectedList.value)
  selectedList.value = []
}

const cancel = () => {
  emits('onCancel')
}
</script>
