<template>
  <div className="json-editor" :style="{ width: props.width || '100%', height: props.height || '300px' }">
    <Codemirror v-model="editorValueLocal" :extensions="extensions" :placeholder="props.placeholder"
      :readOnly="props.readOnly" :theme="props.theme" @ready="e => editor = e.view" />
    <slot name="toolbar"></slot>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, defineEmits, defineProps, onMounted, onBeforeUnmount, nextTick, toRefs } from 'vue'
import { Codemirror } from 'vue-codemirror'
import { json, jsonParseLinter } from '@codemirror/lang-json'
import { linter, lintGutter } from '@codemirror/lint'
// import { autocompletion, CompletionContext, Completion } from '@codemirror/autocomplete'
import { EditorView, ViewUpdate } from '@codemirror/view'

interface Props {
  modelValue: string | object
  readOnly?: boolean
  placeholder?: string
  theme?: string
  height?: string
  width?: string
  // 可扩展更多props
}
const props = defineProps<Props>()
const emit = defineEmits<{
  (e: 'update:modelValue', value: string | object): void
  (e: 'change', value: string, view: EditorView): void
  (e: 'focus', view: EditorView): void
  (e: 'blur', view: EditorView): void
  (e: 'lint', diagnostics: any[]): void
  // (e: 'completion', context: CompletionContext): void
}>()

const format = (value: string | object): string => {
  try {
    const obj = typeof value === 'string' ? JSON.parse(value) : value
    return JSON.stringify(obj, null, 2)
  } catch {
    return value as string
  }
}

const { modelValue } = toRefs(props)
const editorValueLocal = ref(format(modelValue.value))
const editor = ref<EditorView>()

// 外部modelValue变化时，更新本地显示（支持string或object）
watch(modelValue, (val) => {
  const strVal = format(val)
  if (strVal !== editorValueLocal.value) editorValueLocal.value = strVal
})

// 编辑器内容变化时，尝试emit解析后的对象，否则emit字符串
watch(editorValueLocal, (val) => {
  let parsed: object | string = val
  try {
    parsed = JSON.parse(val)
  } catch (_) {
    // 非法JSON时emit字符串
    parsed = val
  }
  // 只有内容不等时emit
  if (parsed !== modelValue.value) emit('update:modelValue', parsed)
})

// 示例：自定义 JSON 自动补全
// function jsonCompletion(context: CompletionContext): Completion[] | null {
//   // 仅在 root 层触发
//   if (context.matchBefore(/"\w*"?\s*:/)) {
//     emit('completion', context)
//     return [
//       { label: '"name"', type: 'property', info: 'JSON 属性 name' },
//       { label: '"age"', type: 'property', info: 'JSON 属性 age' },
//       // ...可自定义
//     ]
//   }
//   return null
// }

// Editor 扩展
const extensions = [
  json(),
  // autocompletion({ override: [jsonCompletion] }),
  lintGutter(),
  linter(jsonParseLinter()),
  EditorView.updateListener.of((v: ViewUpdate) => {
    if (v.docChanged) {
      emit('change', v.state.doc.toString(), v.view)
    }
    if (v.focusChanged && v.view.hasFocus) emit('focus', v.view)
    if (v.focusChanged && !v.view.hasFocus) emit('blur', v.view)
  }),
  props.readOnly ? EditorView.editable.of(false) : [],
]

onMounted(() => {
  nextTick(() => {
    if (editor.value) {
      // 可以在这里做一些 editor 实例相关的初始化
    }
  })
})
onBeforeUnmount(() => {
  // 清理资源
})
</script>

<style scoped>
.json-editor {
  position: relative;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: auto;
}
</style>
