<!-- components/CodeMirrorEditor.vue -->
<template>
  <div ref="editorContainer" class="code-mirror-editor"></div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onBeforeUnmount, watch } from 'vue';
import { createCodeMirror5Editor } from '#/utils/code-mirror'; // 导入之前创建的函数

export default defineComponent({
  name: 'CodeMirrorEditor',

  props: {
    // 编辑器内容（双向绑定）
    modelValue: {
      type: String,
      default: '',
    },
    // 编辑器配置
    options: {
      type: Object,
      default: () => ({}),
    },
    // 只读模式
    readonly: {
      type: Boolean,
      default: false,
    },
  },

  emits: ['update:modelValue', 'change', 'focus', 'blur'],

  setup(props, { emit }) {
    const editorContainer = ref<HTMLElement | null>(null);
    let editor: any = null; // CodeMirror 实例

    // 初始化编辑器
    const initEditor = () => {
      if (!editorContainer.value) return;

      // 创建编辑器实例
      editor = createCodeMirror5Editor(editorContainer.value, {
        value: props.modelValue,
        readOnly: props.readonly,
        ...props.options,
      });

      // 监听编辑器内容变化
      editor.on('change', (instance: any) => {
        const newValue = instance.getValue();
        emit('update:modelValue', newValue);
        emit('change', newValue);
      });

      // 监听焦点事件
      editor.on('focus', () => emit('focus'));
      editor.on('blur', () => emit('blur'));
    };

    // 更新编辑器内容
    const updateEditorContent = (newValue: string) => {
      if (editor && editor.getValue() !== newValue) {
        editor.setValue(newValue);
      }
    };

    // 组件挂载后初始化编辑器
    onMounted(() => {
      initEditor();
    });

    // 监听 props.modelValue 变化
    watch(() => props.modelValue, (newValue) => {
      updateEditorContent(newValue);
    });

    // 监听 readonly 属性变化
    watch(() => props.readonly, (newValue) => {
      if (editor) {
        editor.setOption('readOnly', newValue);
      }
    });

    // 组件卸载前销毁编辑器
    onBeforeUnmount(() => {
      if (editor) {
        editor.toTextArea(); // 销毁编辑器实例
        editor = null;
      }
    });

    return {
      editorContainer,
    };
  },
});
</script>

<style scoped>
.code-mirror-editor {
  height: 300px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 100%
}

/* 确保 CodeMirror 样式正确加载 */
@import 'codemirror/lib/codemirror.css';
@import 'codemirror/theme/monokai.css';
</style>
