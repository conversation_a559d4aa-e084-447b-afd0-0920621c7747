<script lang="ts" setup>
import { computed } from 'vue';

import { AuthPageLayout } from '@vben/layouts';
import { preferences } from '@vben/preferences';

import { $t } from '#/locales';

const appName = computed(() => preferences.app.name);
const subTitle = computed(() => preferences.app.subTitle);
const logo = computed(() => preferences.logo.source);
</script>

<template>
  <AuthPageLayout :app-name="appName" :sub-title="subTitle" :logo="logo"
    :page-description="$t('authentication.pageDesc')" :page-title="$t('authentication.pageTitle')" :copyright="false"
    :toolbar="false">
    <!-- 自定义工具栏 -->
    <!-- <template #toolbar></template> -->
  </AuthPageLayout>
</template>
