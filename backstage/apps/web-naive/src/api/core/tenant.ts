import { requestClient } from '#/api/request';
const prefix = '/tenant'
export interface ValidateInfo {
  email?: string
  password?: string
}

export interface TenantInfo {
  admins?: string[]
  createTime?: number
  createdBy?: string
  deleteTime?: number
  exTenantId?: string
  iconUrl?: string
  id?: number
  tenantName?: string
  status?: string
  tenantId?: string
  updateTime?: number
  updatedBy?: string
}

export interface CreateTenantInfo {
  tenantName?: string
  adminName?: string
  adminEmail?: string
  adminPassword?: string
}

/**
 * 租户查询
 * @param data 租户查询参数
 * @returns
 */
export async function getTenantListApi(data: ValidateInfo) {
  return requestClient.post<TenantInfo[]>(`${prefix}/list`, data);
}

/**
 * 新建租户
 * @param data 租户参数
 * @returns
 */
export async function createTenantApi(data: CreateTenantInfo) {
  return requestClient.post(`${prefix}/create`, data);
}

/**
 * 更新租户
 * @param data 租户参数
 * @returns
 */
export async function updateTenantApi(data: TenantInfo) {
  return requestClient.post(`${prefix}/update`, data);
}

/**
 * 获取邀请配置信息
 * @param code 参数
 * @returns
 */
export async function getActivateInfoApi(code?: string) {
  return requestClient.get(`${prefix}/info/code?code=${code}`);
}

/**
 * 分页获取租户
 * @param data 租户参数
 * @returns
 */
export async function getTenantPageApi(data: { current?: number; size?: number }) {
  return requestClient.post(`${prefix}/page`, data);
}


/**
 * 删除租户
 * @param data 租户参数
 * @returns
 */
export async function deleteTenantApi(data: { tenantId?: string }) {
  return requestClient.delete(`${prefix}/delete?tenantId=${data.tenantId}`);
}
