import { requestClient } from '#/api/request';
import type { PageListParams, PageListResult } from '#/constant/index'

export namespace McpApi {
  export enum McpType {
    SSE = 'sse',
    STDIO = 'stdio',
  }

  export interface CreateItem {
    name: string;
    description?: string;
    type: McpType;
    avatarId?: string;
    configs: Record<string, any>;
  }

  export interface PageItem extends CreateItem {
    id: string;
  }

}

export function getMcpPageApi(data: PageListParams) {
  return requestClient.post<PageListResult<McpApi.PageItem>>('/mcp/page', data);
}

export function deleteMcpApi(id: string) {
  return requestClient.delete(`/mcp/${id}/delete`);
}

export function createMcpApi(data: McpApi.CreateItem) {
  return requestClient.post('/mcp/add', data);
}

export function updateMcpApi(data: McpApi.PageItem) {
  return requestClient.post(`/mcp/update`, data);
}
