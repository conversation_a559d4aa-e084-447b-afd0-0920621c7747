import { requestClient } from '#/api/request';
const prefix = '/department'

export interface DepartmentParams {
  departmentId?: string
  name?: string
  hierarchy?: string
  managerIds?: string[]
}

export interface DepartmentAddMemberParams {
  departmentId?: string
  userIds: string[]
}

export interface DepartmentDelMemberParams {
  departmentId?: string
  userIds: string[]
}

/**
 * 新建部门
 * @param data 部门参数
 * @returns
 */
export async function createDepartmentApi(data: Omit<DepartmentParams, 'departmentId'>) {
  return requestClient.post(`${prefix}/create`, data);
}

/**
 * 获取部门树
 * @returns
 */
export async function getDepartmentTreeApi( departmentId?: string) {
  return requestClient.get(`${prefix}/tree${departmentId ? `?departmentId=${departmentId}` : ''}`);
}

/**
 * 删除部门
 * @param data 部门参数
 * @returns
 */
export async function deleteDepartmentApi(data: { departmentId?: string }) {
  return requestClient.delete(`${prefix}/delete?departmentId=${data?.departmentId}`);
}

/**
 * 更新部门
 * @param data 部门参数
 * @returns
 */
export async function updateDepartmentApi(data: DepartmentParams) {
  return requestClient.post(`${prefix}/update?departmentId=${data?.departmentId}`, data);
}


/**
 * 上传部门树
 * @param data 树参数
 * @returns
 */
export async function uploadDepartmentApi(data: { file?: File }) {
  return requestClient.post(`${prefix}/upload`, data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 批量修改成员的部门
 * @param data 参数
 * @returns
 */
export async function changeDepartmentUserApi(data: DepartmentParams) {
  return requestClient.post(`${prefix}/users/change?departmentId=${data?.departmentId}`, data);
}

/**
 * 部门查询成员
 * @param data 查询参数
 * @returns
 */
export async function searchDepartmentUserApi(data: DepartmentParams) {
  return requestClient.post(`${prefix}/users/search?departmentId=${data?.departmentId}`, data);
}

/**
 * 部门删除成员
 * @param data 查询参数
 * @returns
 */
export async function deleteDepartmentUserApi(data: DepartmentDelMemberParams) {
  return requestClient.delete(`${prefix}/users/delete`, {
    data: {...data}
  });
}


/**
 * 部门添加成员
 * @param data 参数
 * @returns
 */
export async function departmentAddUsersApi(data: DepartmentAddMemberParams) {
  return requestClient.post(`${prefix}/users/change`, data);
}
