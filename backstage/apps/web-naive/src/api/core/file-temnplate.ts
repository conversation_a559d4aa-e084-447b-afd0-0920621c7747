import { requestClient } from '#/api/request';
const prefix = '/file'

/**
 * 获取部门模板
 * @returns
 */
export async function getDepartmentTplApi() {
  const res = await requestClient.download(`${prefix}/department/template`, {
    headers: {
      'Content-Type': 'application/octet-stream'
    },
    // 设置响应类型为 blob
    responseType: 'blob' as 'blob'
  });
  const blob = res;
  // 创建一个 URL 对象，指向 blob 数据
  const url = URL.createObjectURL(blob as any);
  // 创建一个 a 元素
  const a = document.createElement('a');
  a.href = url;
  // 设置下载属性，指定文件名，假设服务器返回的文件名是 example.pdf
  a.download = 'example.pdf';
  // 模拟点击事件进行下载
  a.click();
  // 释放 URL 对象
  URL.revokeObjectURL(url);      
}

/**
 * 获取部门树
 * @returns
 */
export async function getUserTplApi() {
  return requestClient.get(`${prefix}/user/template`);
}

/**
 * 上传安装包
 * @param data 
 * @returns 
 */
 export async function uploadPackageApi(data: FormData) {
  return requestClient.post(`${prefix}/plugin/upload`, data, {
    headers: {
      'Authorization': 'Bearer F28heQN6zFdpGhe2ptmH5sXiy6PYCF21',
      'Content-Type' : 'multipart/form-data'
    }
  });
}
