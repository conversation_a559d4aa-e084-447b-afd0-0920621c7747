import { baseRequestClient, requestClient } from '#/api/request';

export namespace KnowledgeApi {
  interface KnowledgePage<T> {
    total: number,
    current: number,
    size: number,
    records: Array<T>,
    [key: string]: any
  }

  export interface QueryKnowledgeParams {
    name?: string;
    pageNo?: number;
    pageSize?: number;
  }

  interface QueryKnowledgeItem {
    id: string;
    name: string;
    [key: string]: any
  }

  export type QueryKnowledgeResult = KnowledgePage<QueryKnowledgeItem>

  export interface SaveKnowledgeParams {
    name: string;
    knowledgeBaseId: string;
  }

  export interface GetKnowledgePageParams {
    name?: string;
    current?: number;
    size?: number;
  }

  export interface KnowledgePageItem {
    name: string;
    id: string;
    knowledgeBaseId: string;
    createdAt: string;
  }

  export type GetKnowledgePageResult = KnowledgePage<KnowledgePageItem>
}

export async function getAccessTokenApi(token: string) {
  return baseRequestClient.get(`/cnai/login/${token}`);
}

export async function queryKnowledgeListApi(data: KnowledgeApi.QueryKnowledgeParams) {
  return requestClient.post<KnowledgeApi.QueryKnowledgeResult>(`/knowledgeBase/query`, data);
}

export async function saveKnowledgesApi(data: KnowledgeApi.SaveKnowledgeParams[]) {
  return requestClient.post(`/knowledgeBase/save`, data);
}

export async function getKnowledgePageApi(data: KnowledgeApi.GetKnowledgePageParams) {
  return requestClient.post<KnowledgeApi.GetKnowledgePageResult>(`/knowledgeBase/page`, data);
}

export async function deleteKnowledgeApi(id: string) {
  return requestClient.delete(`/knowledgeBase/delete/${id}`);
}
