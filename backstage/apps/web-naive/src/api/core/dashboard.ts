import { requestClient } from '#/api/request';
const prefix = '/dashboard'

export interface DashboardParams {
  startDate?: string
  endDate?: string
}

/**
 * 获取面板数据
 * @param data 查询参数
 * @returns
 */
export async function getTenantDashboardApi(data: DashboardParams) {
  return requestClient.post(`${prefix}/tenant`, data);
}

/**
 * 获取部门面板数据
 * @param data 查询参数
 * @returns
 */
export async function getDepartmentDashboardApi(data: DashboardParams) {
  return requestClient.post(`${prefix}/department`, data);
}


/**
 * 获取用户面板数据
 * @param
 * @returns
 */
export async function getUserDashboardApi() {
  return requestClient.get(`${prefix}/user`);
}
