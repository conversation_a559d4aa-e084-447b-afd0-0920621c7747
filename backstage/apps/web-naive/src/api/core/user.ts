import type { BasicUserInfo } from '@vben/types';
const prefix = '/user'
import { requestClient } from '#/api/request';

export namespace UserApi {
  /** 登录接口参数 */
  export interface LoginParams {
    password?: string;
    email?: string;
    tenantId?: string;
  }

  export interface RegisterParams extends LoginParams {
    email?: string
    password?: string
    name?: string
  }

  export interface ActivateParams {
    name?: string
    password?: string
    email?: string
    code?: string
  }

  /** 登录接口返回值 */
  export interface LoginResult {
    code?: string
    data: string;
  }

  export interface RegisterResult {
    data?: string
  }

  export interface RefreshTokenResult {
    data: string;
    status: number;
  }

  export interface ListPage {
    current?: number
    size?: number
    key?: string
  }

  export interface ListPageResult<T> {
    total: number,
    current: number,
    size: number,
    records: Array<T>,
    [key: string]: any
  }

  export interface DepartmentParams {
    departmentId?: string
    email?: string,
    role?: string,
    name?: string
  }

  export interface CreateUser {
    departmentId?: string
    email?: string,
    name?: string
  }

  export interface ChangeUser {
    departmentId?: string
    role?: string,
    userIds?: string[]
  }
}


/**
 * 登录
 * @param data 登录参数
 * @returns
 */
export async function loginApi(data: UserApi.LoginParams) {
  return requestClient.post(`${prefix}/login`, data);
}

/**
 * 注册
 * @param data 注册参数
 * @returns
 */
export async function registerApi(data: UserApi.RegisterParams) {
  return requestClient.post<UserApi.RegisterResult>(`${prefix}/register`, data);
}

/**
 * 获取用户列表
 * @param data 列表参数
 * @returns
 */
export async function getUserPageApi<T>(data: UserApi.ListPage) {
  return requestClient.post<UserApi.ListPageResult<T>>(`${prefix}/page`, data);
}

/**
 * 获取用户信息
 */
export async function getUserInfoApi() {
  return requestClient.get<BasicUserInfo>(`${prefix}/info`);
}

/**
 * 搜索用户
 * @param data 搜索参数
 * @returns
 */
export async function searchUserApi(data: { key?: string, current?: number, size?: number }) {
  return requestClient.post(`${prefix}/search`, data);
}

/**
 * 删除用户
 * @param data 删除参数
 * @returns
 */
export async function deleteUserApi(data: { userIdList?: string[] }) {
  return requestClient.delete(`${prefix}/delete?userIdList=${data?.userIdList}`);
}

/**
 * 邀请用户
 * @param data 邀请参数
 * @returns
 */
export async function inviteUserApi(data: { departmentId?: string, expirationTime?: number, role?: string }) {
  return requestClient.post(`${prefix}/invite`, data);
}

/**
 * 用户接受邀请
 * @param data 接受邀请参数
 * @returns
 */
export async function activateUserApi(data: UserApi.ActivateParams) {
  const { code, ...rest } = data
  console.log({ rest })
  return requestClient.post(`${prefix}/activate?code=${code}`, rest);
}

/**
 * 文件上传用户
 * @param data 上传参数
 * @returns
 */
export async function uploadUserApi(data: { file?: File }) {
  return requestClient.post(`${prefix}/upload`, data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}


/**
 * 部门用户列表
 * @param data 查询参数
 * @returns
 */
export async function getDepartmentUserApi(data: { departmentId?: string, size?: number, current?: number, key?: string }) {
  return requestClient.post(`${prefix}/department/page`, data);
}

/**
 * 租户添加用户
 * @param data 参数
 * @returns
 */
export async function addTenantUserApi(data: UserApi.DepartmentParams) {
  return requestClient.post(`${prefix}/tenant/add`, data);
}

/**
 * 部门添加成员
 * @param data 参数
 * @returns
 */
export async function createUserApi(data: UserApi.CreateUser) {
  return requestClient.post(`${prefix}/create`, data);
}


/**
 * 批量修改用户部门和角色
 * @param data 参数
 * @returns
 */
export async function changeUserApi(data: UserApi.ChangeUser) {
  return requestClient.post(`${prefix}/batch/change`, data);
}

/**
 * 批量修改用户部门
 * @param data 参数
 * @returns
 */
export async function updateUserApi(data: UserApi.ChangeUser) {
  return requestClient.post(`${prefix}/update`, data);
}

/**
 * 使用DevOps登录
 *
 * @param data 登录所需参数
 * @param data.accessToken 访问令牌
 * @param data.track 跟踪信息
 * @param data.device 设备信息
 * @param data.state_id 状态ID
 * @returns 登录结果
 */
export async function loginWithDevops(data: {
  accessToken: string;
  track: string;
  device: string;
  state_id: string;
}) {
  return requestClient.post('/console/api/devops/login', data)
}
