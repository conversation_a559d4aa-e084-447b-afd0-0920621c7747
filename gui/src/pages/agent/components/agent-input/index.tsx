import React, { ChangeEvent, createRef, useContext, useEffect, useState } from 'react';
import cx from 'classnames';
import { Input, Tooltip } from 'antd';
import { useMemoizedFn } from 'ahooks'
import { CommandPopover } from '@/components/command-popover';
import { CommandSymbols } from '@/constants/command';
import { IdeMessengerContext } from '@/context/ide-messenger'
import type { CommandItem } from '@/types/command';
import { clearSelectCodeRecord } from '@/utils/editor';
import { GUIEvent } from '@/constants/event';
import { CloseCircleOutlined, SendOutlined } from '@ant-design/icons';
import { CommandList } from '@/components/command-list';
import { ExtendedList } from '@/components/chat-input/components/extended-list';
import { ExtendedTag } from '@/components/chat-input/components/extended-tag';
import { StopGenerating } from '@/components/chat-input/components/stop-generating';
import { isDirectiveWithoutContent, getSelectorHeight, getCommandItem } from './utils';
import { AddIcon } from '@/components/icons';
import { Directive } from 'core/protocol/message-event-data';
import { getFolderOptions, getFileOptions, getKnowledgeOptions } from '@/utils/codebase'
import { useCommandOptions, useCommandState, useKnowledge, useFilepath, useToolCall } from '@/hooks'
import { DelKeyList, KeyboardKeys } from '@/constants/keyboard';
import { useChatContext } from '@/context/chat'
import { AgentAllExtendOptions } from '@/constants/command';
import { historyManager } from '@/utils/history';
import { WorkingSpace } from '@/components/working-space'

import './index.less'


/**
 * 聊天输入框组件
 *
 * @param props 组件属性
 * @returns 聊天输入框组件
 */
export const AgentInput: React.FC = () => {
  // 是否在输入 用于阻止中文输入法下输入英文回车会触发请求事件
  const [isComposing, setIsComposing] = useState(false);
  const [open, setOpen] = useState(false);
  const [minimize, setMinimize] = useState(false);
  const { filepath, showFilepath, setShowFilePath } = useFilepath();
  const [popoverStyle, setPopoverStyle] = useState<Record<string, string | number>>({});
  const [tempVal, setTempVal] = useState('');
  const [extendedItem, setExtendedItem] = useState<CommandItem | null>(null);
  const { disabledKeys, defaultFileList, defaultFolderList, allFileList, allFolderList, reloadKnowledge } = useKnowledge()
  const commandOptions = useCommandOptions(disabledKeys, allFileList, allFolderList)
  const {
    commandItem,
    tempCommandItem,
    selectedCommands,
    setSelectedCommands,
    addSelectedCommand,
    setCommandItem,
    setTempCommandItem,
    onDeleteCommand
  } = useCommandState()

  const { clearToolCallCache, editFileList } = useToolCall()

  const defaultFolderOptions = getFolderOptions(defaultFolderList)
  const defaultFileOptions = getFileOptions(defaultFileList)

  const showWorkingSpace = editFileList?.length > 0

  const ideMessenger = useContext(IdeMessengerContext);
  const { isCompleted, isError, isLoading, messages, handleSendMessage: onEnter, cancelRequest, setMessages } = useChatContext();
  const inputRef = createRef<HTMLTextAreaElement>();
  const directiveWithoutContent = isDirectiveWithoutContent(commandItem);
  const disabledInput = tempVal === '' && (!commandItem || commandItem?.children);
  const filterCommandOptions = commandOptions.getFilteredOptions(commandItem)

  const onClearHistory = () => {
    setTempVal('');
    setCommandItem(null);
    setExtendedItem(null);
  };

  useEffect(() => {
    window.addEventListener(GUIEvent.ClearHistory, onClearHistory);
    return () => {
      window.removeEventListener(GUIEvent.ClearHistory, onClearHistory);
    };
  }, [isCompleted, isLoading])

  const setInputPopoverStyle = () => {
    const height = getSelectorHeight('.agent-page-input');
    let workingSpaceHeight = getSelectorHeight('.workspace');

    setMinimize(showWorkingSpace)

    if (showWorkingSpace) {
      workingSpaceHeight += 8
    }

    if (height) {
      setPopoverStyle({ bottom: `${height - workingSpaceHeight}px` });
    }
  };

  const hiddenSelector = () => {
    setOpen(false);
  };

  // TODO: 待完善 
  const showCommandListHandle = () => {
    // setInputPopoverStyle();
    // setOpen(true);
    // inputRef?.current?.focus();
    // setTempCommandItem(null);
    // setCommandItem(null);
  };

  // TODO: 待完善 
  // 指令列表过滤
  const matchCommandHandle = (val: string, options?: CommandItem[]) => {
    // const list = options ? options : commandOptions.options;
    // const matchList = list?.filter((i) => i?.label?.match(val));
    // if (matchList?.length > 0) {
    //   commandOptions.setOptions(matchList);
    //   setOpen(true);
    // } else {
    //   hiddenSelector();
    // }
  };

  /**
   * 文本域变化时触发的方法
   *
   * @param e 文本域变化的事件对象
   */
  const onTextareaChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    const val = e.target.value;
    const isSlashCommand = val.startsWith(CommandSymbols.slash);
    const isAtCommand = val.startsWith(CommandSymbols.at);
    const isHashCommand = val.startsWith(CommandSymbols.hash);
    const { children } = commandItem || {};
    setTempVal(val);

    // 当前存在选中项
    if (commandItem) {
      if (children) {
        commandOptions.setOptions(children);
        val && matchCommandHandle(val);
      }
    } else {
      hiddenSelector();
    }

    if (isAtCommand || isSlashCommand || isHashCommand) {
      if (val.length === 1) {
        commandOptions.initializeOptions(val)
        showCommandListHandle();
      } else {
        matchCommandHandle(val);
      }
    }
  };
  // 关闭选中标签
  const closeFilePathLabel = () => {
    setShowFilePath(false);
    clearSelectCodeRecord();
    ideMessenger.post("feature/cancelEditorSelection", undefined)
  };

  const onPressEnter = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // 阻止默认行为
    e.preventDefault();
    if (open || disabledInput || Object.values(CommandSymbols).includes(tempVal)) {
      setTempVal('');
      return;
    }
    // 阻止冒泡
    e.stopPropagation();
    if (e.altKey || e.metaKey) return;
    // 检测是否正在输入中
    if (isComposing) return;

    if (directiveWithoutContent) {
      return ideMessenger.ide.showToast("warn", "请框选包含代码内容的片段!");
    }
    onEnterHandle();
    closeFilePathLabel();
  };

  /**
   * 处理回车事件
   *
   * @returns 无返回值
   */
  const onEnterHandle = () => {
    if (!isCompleted) {
      cancelRequest();
    }
    setSelectedCommands([])
    setTempVal('');
    setCommandItem(null);
    setTempCommandItem(null);
    onEnter && onEnter(tempVal, getCommandItem(selectedCommands));
  };

  const onRefresh = useMemoizedFn(async () => {
    if (tempCommandItem?.value !== Directive.KnowledgeBase) {
      reloadKnowledge()
    } else {
      getKnowledgeOptionsFn()
    }
  })

  const onReturn = useMemoizedFn(() => {
    const parent = AgentAllExtendOptions.find(item => item.value === tempCommandItem?.parentValue);
    if (parent) {
      onExtendedItemClick(parent);
      setTimeout(() => {
        setOpen(true);
      }, 100);
    }
  })


  const getKnowledgeOptionsFn = useMemoizedFn(async () => {
    const list = await getKnowledgeOptions();
    if (tempCommandItem?.value === Directive.KnowledgeBase && !tempCommandItem?.id) {
      commandOptions.setOptions(list)
    }
  })

  const onExtendedItemClick = useMemoizedFn((item: CommandItem) => {
    setCommandItem(null);
    setTempCommandItem(null);
    setExtendedItem(item);
    setOpen(!open);
    // 指令点击后 点击插件或其它按钮的时候需要重新打开指令列表
    if (open && item.value !== extendedItem?.value) {
      setTimeout(() => {
        setOpen(true);
      }, 100);
    }
    if (item?.children) {
      commandOptions.setOptions(item.children);
    }
    setInputPopoverStyle();
  });

  const onAddConversation = useMemoizedFn(() => {
    const addFn = () => {
      // 无有效会话时，清空消息
      if (!historyManager.getConversationId()) {
        setMessages([])
      }
      historyManager.addConversation()
      inputRef?.current?.focus();
    }
    if (isLoading || !isCompleted) {
      cancelRequest(() => {
        setTimeout(() => {
          addFn()
        }, 200)
      });
      return
    }
    addFn()

    clearToolCallCache()
  })


  const handleKeyDown = useMemoizedFn((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // 换行操作
    if ((e.metaKey || e.altKey) && e.key === KeyboardKeys.Enter) {
      setTempVal(tempVal + '\n');
      e.preventDefault();
      e.stopPropagation();
      return;
    }
    // 如果当前指令列表打开时  禁用上下箭选择 避免和指令上下键冲突
    if (open && ([KeyboardKeys.ArrowUp, KeyboardKeys.ArrowDown].includes(e.key))) {
      e.preventDefault();
      return;
    }
    const target = e.target as HTMLTextAreaElement;
    const len = target.value.length;
    // 删除检测 清空指令
    if (DelKeyList.includes(e.key) && len === 0) {
      const newCommands = [...selectedCommands]
      newCommands.pop()
      setSelectedCommands(newCommands)
      setCommandItem(null);
      setTempCommandItem(null);
      hiddenSelector();
    }
  });

  /**
 * 当命令项发生变化时触发
 *
 * @param item 命令项对象
 */
  const onCommandChange = useMemoizedFn(async (item: CommandItem) => {
    if (item?.children) {
      commandOptions.setOptions(item.children);
    } else {
      hiddenSelector();
      inputRef.current?.focus();
      addSelectedCommand(item);
    }
    if (item.value === Directive.ProjectQADirectory) {
      commandOptions.setOptions(defaultFolderOptions)
    }
    if (item.value === Directive.ProjectQAFile) {
      commandOptions.setOptions(defaultFileOptions)
    }

    setCommandItem(item);
    setTempCommandItem(item)
    commandOptions.setSearchText('')

    // 点击知识集
    if (item.value === Directive.KnowledgeBase && !item.id) {
      getKnowledgeOptionsFn();
    }
  });


  return (
    <div className='chat-input-wrapper'>
      {!isCompleted && !isError && <StopGenerating cancelRequest={cancelRequest} />}
      {showWorkingSpace && <WorkingSpace minimize={minimize} setMinimize={setMinimize} />}

      <div className='chat-input-inner'>
        {/* {showFilepath && (
          <div className='chat-input-file'>
            <div className='filepath'>{filepath}</div>
            <div className='close-icon' onClick={() => closeFilePathLabel()}>
              <CloseCircleOutlined />
            </div>
          </div>
        )} */}
        <div className='chat-input-extended'>
          <CommandPopover open={open}
            onBlur={() => {
              setOpen(false)
              commandOptions.setSearchText('')
              setTempCommandItem(null)
              if (commandItem?.children) {
                setCommandItem(null)
              }
            }}
            style={popoverStyle}>
            {open && (
              <CommandList
                value={commandItem?.children ? commandItem?.children[0]?.value : (commandItem?.value as string)}
                showSearch={tempCommandItem?.showSearch}
                showHeader={tempCommandItem?.showSearch}
                options={filterCommandOptions}
                onChange={onCommandChange}
                onSearch={commandOptions.onSearch}
                onRefresh={onRefresh}
                showFileIcon={tempCommandItem?.value === Directive.ProjectQAFile}
                showDescOnHover={[Directive.ProjectQAFile, Directive.ProjectQADirectory, Directive.KnowledgeBase].includes(tempCommandItem?.value as Directive)}
                title={tempCommandItem?.label || ''}
                onReturn={tempCommandItem?.parentValue ? onReturn : null}
              />
            )}
          </CommandPopover>
        </div>

        <div className='chat-input-selector'>
          <div className='select-selector'>
            <div className='select-selection-search'>
              {selectedCommands && <ExtendedTag selectedCommands={selectedCommands} onDelete={onDeleteCommand} />}
              <Input.TextArea
                ref={inputRef}
                size='small'
                value={tempVal}
                className='textarea'
                placeholder={commandItem?.placeholder || '输入或粘贴问题进行提问、编码'}
                onChange={onTextareaChange}
                onPressEnter={onPressEnter}
                onKeyDown={handleKeyDown}
                autoSize
                onCompositionStart={() => setIsComposing(true)}
                onCompositionEnd={() => setIsComposing(false)}
              ></Input.TextArea>
            </div>
          </div>
        </div>

        <div className='chat-input-extended_area'>
          <div className='extended-left'>
            <ExtendedList
              disabledKeys={disabledKeys}
              open={open}
              inputValue={tempVal}
              onExtendedItemClick={onExtendedItemClick}
              options={AgentAllExtendOptions}
            />
          </div>
          <div className='extended-right'>
            {
              messages?.length > 0 &&
              <div className='toolbar-btn add-session' onClick={() => onAddConversation()}>
                <Tooltip title='新建对话'>
                  <span><AddIcon /></span>
                </Tooltip>
              </div>
            }

            <div className='toolbar-btn'>
              <span
                className={cx('chat-input-button ', { ['disabled']: disabledInput })}
                onClick={() => {
                  if (disabledInput) return;
                  if (directiveWithoutContent) {
                    return ideMessenger.ide.showToast("warn", "请框选包含代码内容的片段")
                  }
                  onEnterHandle();
                  closeFilePathLabel();
                }}
              >
                <SendOutlined />
              </span>
            </div>
          </div>
        </div>
      </div>
    </div >
  );
};
