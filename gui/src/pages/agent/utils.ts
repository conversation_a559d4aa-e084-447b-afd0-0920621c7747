import { AllCommandsConfigMap, CommandKey, GenerationLocation } from 'core/constants/command'
import { ResponseMode } from 'core/constants/api-response'
import InternalAnalytics, { CaptureBaseParams } from 'core/analytic/internal-analytics-provider';
import { AnalyticOperation, AskMethod } from 'core/analytic/constant';
import { toServerTime } from '@/utils/to-server-time';
import { historyManager } from '@/utils/history';
import { TriggerMethod } from 'core/analytic/constant';
import { ChatMessage, FromServerItem } from '@/types';

// 获取指令的配置信息
export const getCommandConfig = (commandKey: CommandKey | null) => {
  return commandKey ? AllCommandsConfigMap[commandKey as CommandKey] : {
    responseMode: ResponseMode.STREAMING,
    generationLocation: GenerationLocation.BACKEND,
  };
}

/**
 * 捕获问答操作
 *
 * @param triggerMethod 触发方法
 * @param language 语言
 * @param command 具体指令
 * @param track_id 可选的跟踪ID
 */
export const captureAskOperation = (
  triggerMethod: string,
  language: string,
  command: string,
  track_id?: string,
  message_id?: string,
  otherParams?: CaptureBaseParams
) => {
  const operation = command ? command : AnalyticOperation.ASK;
  InternalAnalytics.capture(operation, {
    track_id,
    client_get_response_time: toServerTime(Date.now()),
    conversation_id: historyManager.getConversationId(),
    message_id,
    operate_time: toServerTime(Date.now()),
    ask_method: command ? AskMethod.COMMAND : AskMethod.NORMAL,
    trigger_method: triggerMethod,
    code_language: language,
    ...otherParams
  });
};

export const captureAskHandle = (payload, language: string, value: string, trackId: string) => {
  captureAskOperation(
    payload ? TriggerMethod.CONTEXT_MENU : TriggerMethod.QUESTION_BOX,
    language || '',
    value,
    trackId
  );
}

export const captureMcpToolCall = (operation, params) => {
  const {
    payload,
    trackId,
    language,
    messageId,
    value,
    command,
    ...rest
  } = params || {}
  const triggerMethod = payload ? TriggerMethod.CONTEXT_MENU : TriggerMethod.QUESTION_BOX

  InternalAnalytics.capture(operation, {
    track_id: trackId,
    client_get_response_time: toServerTime(Date.now()),
    conversation_id: historyManager.getConversationId(),
    message_id: messageId,
    operate_time: toServerTime(Date.now()),
    ask_method: command ? AskMethod.COMMAND : AskMethod.NORMAL,
    trigger_method: triggerMethod,
    code_language: language,
    ...rest
  });
}

export const getMessagesByChunk = (chunks, messages: ChatMessage[], isCompleted: boolean) => {
  const answerStr = chunks.map(i => i?.data?.content?.text);
  let chatMessageItem = null
  let lastMsg = chunks?.[chunks.length - 1]
  let messageId = historyManager.getUuid() || lastMsg?.metaData?.messageId


  let toolCalls = []
  let toolResults = []

  chunks?.forEach(i => {
    if (i?.data?.toolCall) {
      toolCalls.push(i.data.toolCall)
    }
    if (i?.data?.toolResult) {
      toolResults.push(i.data.toolResult)
    }
  });

  let extraData = {
    ...lastMsg,
    metaData: {
      ...lastMsg?.metaData,
      messageId: messageId,
    },
    data: {
      ...lastMsg?.data,
      toolCalls,
      toolResults
    }
  }

  chatMessageItem = messages?.find((i: ChatMessage) => i.uuid === messageId);

  const { message_id } = extraData || {};
  const serverItem = chatMessageItem?.fromServer?.find(
    (i: FromServerItem) => i.message_id === message_id
  );

  const tempServer = {
    ...serverItem,
    ...extraData,
    isCompleted,
    timestamp: Date.now(),
    answer: answerStr?.length ? answerStr.join('') : ''
  };

  const fromServerList = chatMessageItem?.fromServer?.length
    ? chatMessageItem?.fromServer.map((i, idx) => {
      if (idx === chatMessageItem?.fromServer?.length - 1) {
        return {
          ...i,
          ...tempServer
        }
      }
      return i
    })
    : [tempServer];

  const resItem = {
    ...chatMessageItem,
    fromServer: fromServerList
  };

  const resList = messages?.map((i: ChatMessage) => {
    if (i.uuid === messageId) {
      return resItem;
    }
    return i;
  });

  return resList;
}

export const getMessagesToolDiffById = (messages: ChatMessage[], messageId: string, tooCallId: string, diff: any) => {
  const list = messages?.map((i: ChatMessage) => {
    if (i.uuid === messageId) {
      let serverList = i.fromServer?.map((v) => {
        if (v.metaData?.messageId === messageId) {
          return {
            ...v,
            data: {
              ...v.data,
              diffs: v.data?.diffs?.length > 0 ? [...v.data?.diffs, diff] : [diff]
            }
          }
        }
        return v
      })
      return {
        ...i,
        fromServer: serverList
      };
    }
    return i;
  });
  return list
}