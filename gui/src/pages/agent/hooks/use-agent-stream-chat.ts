
import { useState, useRef } from 'react';
import { useIdeMessenger } from '@/context/ide-messenger'
import { LLM_MSG_TAG, LLM_MSG_TYPE } from 'core/constants/llm'
import { dispatchAgentEndOfStream } from '@/utils/event';
import { useChatStore, useWebviewListener } from '@/hooks';
import { MessageType, ToolResult, StreamMessage, ToolCallRequestMessage } from 'core/agent/stream'
import { toolCacheManager } from '@/utils/mcp-tools-cache';
import { NeedConfirmedTools } from 'core/agent/constants/tool'
import { onToolCallCancel, onToolCallOk, } from '@/utils/mcp'
import { useMemoizedFn } from 'ahooks'

import type { AgentRequestParams } from 'core';
import { ToolCallStructure } from 'core/agent/stream/types/agent-stream';

const getNewChunk = (chunk, text) => {
  return {
    ...chunk,
    data: {
      ...chunk.data,
      content: {
        ...chunk.data.content,
        text: text
      }
    }
  }
}

export const useAgentStreamChat = () => {
  // 状态管理
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState(false);
  const [streamHasError, setStreamHasError] = useState(false);
  const [isCompleted, setIsCompleted] = useState(true);
  const [streamChunks, setStreamChunks] = useState([])

  // Ref管理
  const currentMessageIdRef = useRef<string | null>(null);
  const tempUserInputParams = useRef(null)

  const { chatStoreSnap } = useChatStore()
  const ideMessenger = useIdeMessenger();


  const initChatStatus = useMemoizedFn(() => {
    setIsError(false);
    setIsCompleted(true);
    setIsLoading(false);
    setStreamHasError(false);
  })

  const sendAgentMessage = useMemoizedFn(async (message: string, params: any) => {
    // 如果上一次还没完成，先取消
    if (!isCompleted && currentMessageIdRef.current) {
      try {
        ideMessenger.request('agent/cancelMessage', { messageId: currentMessageIdRef.current });
      } catch (e) {
        console.warn('cancel previous message failed', e);
      }
      initChatStatus(); // 重置状态
    }

    setIsLoading(true);
    setIsCompleted(false);
    // 清空旧数据
    setStreamChunks([])
    const { id, conversationId } = params;

    tempUserInputParams.current = {
      ...params,
      ...chatStoreSnap?.messageIds
    }
    currentMessageIdRef.current = id; // 记录当前消息 id

    const agentParams: AgentRequestParams = {
      request: message,
      messageId: id,
      conversationId,
      extraData: {
        context: {
          files: params?.params?.inputs?.files || [],
        },
        aiRules: params?.params?.inputs?.aiRules
      }
    }
    ideMessenger.request('agent/sendMessage', { params: agentParams })
  })

  const endChat = useMemoizedFn(() => {
    if (currentMessageIdRef.current) {
      ideMessenger.request('agent/cancelMessage', { messageId: currentMessageIdRef.current })
    }
    currentMessageIdRef.current = null;
    initChatStatus()
  })

  const onMessageEnd = useMemoizedFn(() => {
    initChatStatus()
    currentMessageIdRef.current = null;

    setTimeout(() => {
      dispatchAgentEndOfStream()
    }, 1000)
  })



  useWebviewListener({
    "agent/receiveMessage": async ({ payload }) => {
      await handleReceiveMessage(payload)
    }
  }, [])

  const handleReceiveMessage = useMemoizedFn(async (payload: string) => {
    try {
      console.log('agent/receive==>payload==>', payload);

      // 输出错误
      if (LLM_MSG_TYPE.ERROR_OF_STREAM == payload) {
        initChatStatus()
        currentMessageIdRef.current = null;
        return;
      }

      // 解析并类型化消息块
      let chunk: StreamMessage;
      try {
        chunk = JSON.parse(payload);
      } catch (parseError) {
        console.error('解析消息 payload 失败:', parseError, '原始 payload:', payload);
        setIsError(true);
        return;
      }

      const isToolCallRequest = chunk.messageType === MessageType.TOOL_CALL_REQUEST;
      const { messageType } = chunk;

      // 输出结束
      if (messageType === MessageType.MESSAGE_END) {
        onMessageEnd()
        return
      }

      // 用户输入的消息不处理，直接返回
      if (messageType === MessageType.HUMAN_MESSAGE) {
        return
      }

      let processedChunk = chunk;

      // 工具调用结果
      if (messageType === MessageType.TOOL_CALL_RESULT) {
        const toolResult: ToolResult = chunk.data.toolResult;
        const toolResultStr = `${LLM_MSG_TAG.mcpToolsResponse.start}${JSON.stringify(toolResult)}${LLM_MSG_TAG.mcpToolsResponse.end}`;
        processedChunk = getNewChunk(chunk, toolResultStr);

        // 更新工具调用缓存
        if (toolResult?.id && toolCacheManager.has(toolResult.id)) {
          toolCacheManager.afterToolCall(toolResult.id, toolResult);
        }
      }

      // 处理工具调用请求
      if (isToolCallRequest) {
        const toolCall: ToolCallStructure = (chunk as ToolCallRequestMessage).data.toolCall;
        const toolCallStr = `${LLM_MSG_TAG.mcpToolsCall.start}${JSON.stringify([toolCall])}${LLM_MSG_TAG.mcpToolsCall.end}`;
        processedChunk = getNewChunk(chunk, toolCallStr);

        // 更新工具调用缓存
        if (toolCall?.id) {
          toolCacheManager.beforeToolCall(toolCall.id, toolCall);
        }
      }

      // 批量更新状态，减少渲染次数
      setIsCompleted(false);
      setIsLoading(false);
      setIsError(false);
      setStreamChunks(prev => [...prev, processedChunk]);

      // 自动确认不需要确认的工具调用
      if (isToolCallRequest) {
        const toolCall = (chunk as ToolCallRequestMessage).data.toolCall;
        if (toolCall?.name && !NeedConfirmedTools.map(t => t.toString()).includes(toolCall.name)) {
          onToolCallOk(toolCall);
        }
      }

    } catch (error) {
      console.log('error', error);
    }
  })

  return {
    isError,
    isCompleted,
    isLoading,
    streamHasError,
    setIsLoading,
    sendAgentMessage,
    endChat,
    onToolCallOk,
    onToolCallCancel,
    streamChunks
  };
};
