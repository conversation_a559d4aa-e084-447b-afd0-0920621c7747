import { useContext, useEffect, useState } from "react";
import { IdeMessengerContext } from '@/context/ide-messenger'
import { type McpToolCallCacheItem } from "core/agent/mcp/utils/cache"
import { ChatMessage } from "@/types";
import { BuiltInToolNames } from "core/agent/constants/tool";
import { useSnapshot } from "valtio"
import { mcpToolCacheStore } from "@/store/mcp-tool-cache";
import { toolCacheManager } from "@/utils/mcp-tools-cache";
export const useToolCall = () => {
  const [editFileList, setEditFileList] = useState<McpToolCallCacheItem[]>([]);
  const { cache } = useSnapshot(mcpToolCacheStore);
  const ideMessenger = useContext(IdeMessengerContext);

  const fileShowDiff = (path: string, content: string) => {
    if (!path || !content) return
    ideMessenger.post('file/showDiff', { path, content })
  }
  const acceptFileDiff = (path: string) => {
    if (!path) return
    ideMessenger.post('file/acceptDiff', { path })
  }
  const rejectFileDiff = (path: string) => {
    if (!path) return
    ideMessenger.post('file/rejectDiff', { path })
  }
  const openFile = (path: string) => {
    if (!path) return
    ideMessenger.post('file/open', { path })
  }
  const getToolCache = (toolCallId: string): McpToolCallCacheItem => {
    return toolCacheManager.get(toolCallId)
  }

  const getWorkspaceDirs = async () => {
    const workspaceDirs = await (await ideMessenger.request('query/workspaceDirs', undefined)).content;
    return workspaceDirs
  }

  const getToolCacheResult = (toolCallId: string) => {
    let cacheItem = getToolCache(toolCallId)
    const toolResult = cacheItem?.toolResult
      ? cacheItem?.toolResult
      : null
    return toolResult
  }
  // 会话历史存储的结果
  const getToolResultStatus = (messages, messageId, toolCallId) => {
    if (!messageId) return null
    const currentItem = messages?.find(i => i.uuid === messageId)
    const toolResults = currentItem?.fromServer[0]?.data?.toolResults || {}
    const toolResult = toolResults?.find(i => i.toolCallId === toolCallId)

    return toolResult?.status
  }

  const getHistoryDiff = (messages: ChatMessage[], messageId: string, toolCallId: string) => {
    const currentItem = messages?.find(i => i.uuid === messageId)
    const historyData = currentItem?.fromServer[0]?.data || {}
    const historyDiff = historyData?.diffs?.find(i => i?.id === toolCallId)

    return historyDiff
  }

  const getToolCallStatus = (messages: ChatMessage[], messageId: string, toolCallId: string) => {
    const cache = getToolCacheResult(toolCallId);
    if (cache) {
      return cache?.status;
    }
    return getToolResultStatus(messages, messageId, toolCallId);
  }
  const clearToolCallCache = () => {
    toolCacheManager.clear();
  }

  useEffect(() => {
    const values = Object.values(cache)
    const result = values.filter(i => i.toolCall.name === BuiltInToolNames.EditFile)
    setEditFileList(result)
  }, [cache])


  return {
    fileShowDiff,
    openFile,
    acceptFileDiff,
    rejectFileDiff,

    getToolResultStatus,
    getToolCache,
    getToolCacheResult,
    getHistoryDiff,
    getToolCallStatus,
    getWorkspaceDirs,
    clearToolCallCache,
    editFileList
  }
} 