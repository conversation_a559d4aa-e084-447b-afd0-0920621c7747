import { ToolResult, Tool<PERSON>all } from 'core/agent/stream/types/agent-stream'
import { mcpToolCacheStore as store } from '@/store/mcp-tool-cache';

export type DiffStatus = 'accepted' | 'rejected' | 'pending';

export interface DiffRecord {
  path?: string;
  content?: string
  status?: DiffStatus,
  added?: number,
  deleted?: number,
}

export interface McpToolsCacheItem {
  isLoading: boolean;
  isCompleted: boolean;
  isTriggered: boolean;
  isPending: boolean;
  toolCall: ToolCall | undefined;
  toolResult: ToolResult | undefined;
  diff?: DiffRecord | undefined;
}

class ToolCacheManager {
  public get(key: string) {
    return store.cache[key]
  }

  public set(key: string, value: McpToolsCacheItem) {
    store.cache[key] = value
  }

  public delete(key: string) {
    delete store.cache[key];
  }

  public has(key: string) {
    return !!store.cache[key];
  }
  public clear() {
    Object.keys(store.cache).forEach(key => {
      delete store.cache[key];
    });
  }

  setToolCallDiffInfo(id: string, diffInfo: DiffRecord) {
    const cacheItem = this.get(id);

    if (cacheItem) {
      this.set(id, {
        ...cacheItem,
        diff: {
          ...cacheItem.diff,
          ...diffInfo
        }
      });
    } else {
      this.set(id, {
        isLoading: false,
        isCompleted: false,
        isTriggered: false,
        isPending: true,
        toolCall: undefined,
        toolResult: undefined,
        diff: diffInfo
      });
    }
  }

  // 调用前设置值
  beforeToolCall(id: string, toolCall: ToolCall) {
    const cacheItem = this.get(id);

    if (cacheItem) {
      this.set(id, {
        ...cacheItem,
        isLoading: false,
        isCompleted: false,
        isTriggered: false,
        isPending: true,
      });
      this.setToolCall(id, toolCall);
    } else {
      this.set(id, {
        isLoading: false,
        isCompleted: false,
        isTriggered: false,
        isPending: true,
        toolCall: toolCall,
        toolResult: undefined,
        diff: undefined,
      });
    }
  }

  onToolCall(id: string) {
    const cacheItem = this.get(id);

    if (cacheItem) {
      this.set(id, {
        ...cacheItem,
        isLoading: false,
        isCompleted: true,
        isTriggered: true,
        isPending: false,
      });
    }
  }

  // 调用后设置值
  afterToolCall(id: string, toolResult: ToolResult) {
    const cacheItem = this.get(id)

    if (cacheItem) {
      this.set(id, {
        ...cacheItem,
        isLoading: false,
        isCompleted: true,
        isTriggered: true,
        isPending: false,
      });
      this.setToolResult(id, toolResult);
    }
  }

  setToolCall(id: string, toolCall: ToolCall) {
    const cacheItem = this.get(id);

    if (cacheItem) {
      this.set(id, {
        ...cacheItem,
        toolCall: toolCall,
      });
    }
  }

  setToolResult(id: string, toolResult: ToolResult) {
    const cacheItem = this.get(id);

    if (cacheItem) {
      this.set(id, {
        ...cacheItem,
        toolResult: toolResult,
      });
    }
  }
  getValues() {
    return Object.values(store.cache);
  }
}

export const toolCacheManager = new ToolCacheManager();