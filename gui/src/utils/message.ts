import type { CommandItem } from '@/types/command';
import { ChatMessage, FeedbackType, FromServerItem, MessageCommand } from '@/types/message';
import { v4 as uuidv4 } from 'uuid';
import { BlockingResponse } from 'core/constants/api-response'
import { Git, Plugin } from 'core/protocol/message-event-data';
import { getGitCommitTips } from '@/utils/git';
import { GitInfo } from 'core'
import { PluginIntroduceList } from '@/constants/command';
import { getToolboxAnswer } from '@/plugin/toolbox'
import type { BufferObject } from 'core/utils/event-stream';
import moment from 'moment';

interface DataChangeProps {
  id?: string;
  timestamp?: string | number;
  isCompleted: boolean;
  extraData: Record<string, any>;
  chatMessageItem: ChatMessage;
  answerString: string[];
  tempMessages?: ChatMessage[];
  chunks?: any[];
}

export interface FrontendMessageParams {
  input: string,
  id: string,
  messages: ChatMessage[],
  commandItem: CommandItem | MessageCommand | null | undefined,
  gitInfo: GitInfo | null
}

/**
 * 根据数据变化获取聊天消息列表
 *
 * @param props 数据变化属性
 * @returns 聊天消息列表
 */
export const getMessagesOnDataChange = (props: DataChangeProps): ChatMessage[] => {
  const { id, extraData, chatMessageItem, answerString, tempMessages, isCompleted, timestamp } =
    props || {};

  const { message_id } = extraData || {};
  const serverItem = chatMessageItem?.fromServer?.find(
    (i: FromServerItem) => i.message_id === message_id
  );
  const tempServer = {
    ...serverItem,
    ...extraData,
    isCompleted,
    timestamp,
    answer: answerString?.length ? answerString.join('') : ''
  };
  const fromServerList = chatMessageItem?.fromServer?.length
    ? [...chatMessageItem?.fromServer, tempServer]
    : [tempServer];

  const resItem = {
    ...chatMessageItem,
    fromServer: fromServerList
  };

  const resList = tempMessages?.map((i: ChatMessage) => {
    if (i.uuid === id) {
      return resItem;
    }
    return i;
  });

  return resList as ChatMessage[];
};

const createFromServerItem = (answer: string, response?: BlockingResponse | BufferObject) => {
  const { conversation_id, message_id, task_id, id, created_at } = response || {};
  return {
    event: 'localMessage',
    answer,
    message_id: message_id || uuidv4(),
    conversation_id: conversation_id,
    timestamp: moment().valueOf(),
    created_at: created_at || Math.floor(new Date().getTime() / 1000),
    id: id,
    isCompleted: true,
    conversationId: conversation_id,
    messageId: message_id || uuidv4(),
    taskId: task_id,
    task_id: task_id
  };
};


export const getNewMessageFromFrontend = async (params: FrontendMessageParams, response: BlockingResponse | BufferObject) => {
  const { messages, input, id, gitInfo, commandItem } = params || {};
  let answer = '';

  // 生成提交
  if (commandItem?.value === Git.GenerateGitCommit) {
    answer = getGitCommitTips(gitInfo);
  }
  // 工具箱
  if (commandItem?.parentValue === Plugin.Toolbox) {
    answer = await getToolboxAnswer(input, commandItem)
  }
  // 插件说明介绍
  if (PluginIntroduceList.includes(commandItem?.value as any)) {
    answer = commandItem?.content || '';
  }
  const messageItem = createFromServerItem(answer, response);

  const list = messages.map((item) => {
    if (item.uuid === id) {
      return {
        ...item,
        fromServer: item?.fromServer?.length ? [...item?.fromServer, messageItem] : [messageItem]
      };
    }
    return item;
  });
  return list;
}


/**
 * 更新指定消息的反馈类型
 *
 * @param uuid 消息的唯一标识符
 * @param messageId 要更新反馈类型的消息ID
 * @param messages 聊天消息数组
 * @param value 新的反馈类型
 * @returns 更新后的聊天消息数组
 */
export const updateFeedbackTypeForMessage = (
  uuid: string,
  messageId: string,
  messages: ChatMessage[],
  value: FeedbackType
): ChatMessage[] => {
  const list = messages?.map((i: ChatMessage) => {
    if (i.uuid === uuid) {
      const tempFromServer = i?.fromServer?.map((v: FromServerItem) => {
        if (v.messageId === messageId) {
          return { ...v, feedbackType: value }
        }
        return v
      })
      return {
        ...i,
        fromServer: tempFromServer
      };
    }
    return i;
  });
  return list as ChatMessage[];
}
