import { Directive, Plugin, Git, Toolbox, Knowledge } from 'core/protocol/message-event-data';

export const CommandSymbols = {
  at: '@',
  slash: '/',
  hash: '#',
  add: '+',
};

// 安全检查
const SecurityCheckItem = {
  label: '/安全检查',
  value: Directive.SecurityCheck,
  desc: '',
  placeholder: '',
  text: '代码安全检查',
  prefix: CommandSymbols.slash
}

// 函数拆分
const SplitFunctionItem = {
  label: '/函数拆分',
  value: Directive.FunctionSplit,
  desc: '',
  placeholder: '',
  text: '请拆分以下代码为多个函数',
  prefix: CommandSymbols.slash
}

// 添加日志
const AddLogItem = {
  label: '/添加日志',
  value: Directive.AddLog,
  desc: '',
  placeholder: '',
  text: '请为下面这段代码添加日志',
  prefix: CommandSymbols.slash
}

const FixCodeItem = {
  label: '/快速修复',
  value: Directive.FixCode,
  desc: '',
  placeholder: '',
  text: '请修复下面这段代码中的错误',
  prefix: CommandSymbols.slash
}

// 指令列表
export const DirectiveOptions = [
  {
    label: '/函数注释',
    value: Directive.FunctionComments,
    desc: '',
    placeholder: '框选代码后点击回车或点击发送',
    text: '给下面这个函数添加注释，包括函数参数',
    prefix: CommandSymbols.slash
  },
  {
    label: '/代码解释',
    value: Directive.CodeExplanation,
    desc: '',
    placeholder: '框选代码后点击回车或点击发送',
    text: '请解释如下代码',
    prefix: CommandSymbols.slash
  },
  {
    label: '/行间注释',
    value: Directive.CodeComments,
    desc: '',
    placeholder: '框选代码后点击回车或点击发送',
    text: '为下面这段代码添加行间注释',
    prefix: CommandSymbols.slash
  },
  {
    label: '/代码优化',
    value: Directive.CodeOptimize,
    desc: '',
    placeholder: '框选代码后点击回车或点击发送',
    text: '优化下面这段代码',
    prefix: CommandSymbols.slash
  },
  {
    label: '/生成单测',
    value: Directive.GenerateUnitTest,
    desc: '',
    placeholder: '框选代码后点击回车或点击发送',
    text: '请为下面的函数生成单测',
    prefix: CommandSymbols.slash
  },
];

export const AllDirectiveOptions = [
  ...DirectiveOptions,
  SecurityCheckItem,
  SplitFunctionItem,
  AddLogItem,
  FixCodeItem,
]

export const FunctionCodeLensDirective = [
  ...DirectiveOptions,
  SplitFunctionItem,
  AddLogItem,
]

const gitIntroduceContent = `
# Git助手功能

您好，我是您的 Git 助手，目前支持以下能力：

**Commit Message 生成**
`;

const toolboxIntroduceContent = `
# 工具箱助手功能

您好，我是您的工具箱助手，我提供开发过程中趁手的工具，开发过程中难免需要和各种数据转换打交道，当前我提供一下能力：

Base64编码:
  调用这个能力时，直接输入需要进行Base64编码的的文本即可

Base64解码:
  调用这个能力时，直接输入需要进行Base64解码的的文本

MD5编码:
  调用这个能力时，直接输入需要进行MD5编码的文本

EncodeURI编码 :
  调用这个能力时，直接输入需要进行EncodeURI编码的的文本即可

EncodeURIComponent编码:
  调用这个能力时，直接输入需要进行EncodeURIComponent编码的的文本即可

DecodeURI解码:
  调用这个能力时，直接输入需要进行DecodeURI解码的的文本

DecodeURIComponent解码:
  调用这个能力时，直接输入需要进行DecodeURIComponent解码的的文本

JSON转YAML:
  直接输入JSON示例，我将解析Json并自动生成对应的YAML类型

YAML转JSON:
  直接输入YAML示例，我将解析YAML并自动生成对应的JSON类型

JSON转TS类型:
  直接输入JSON示例，我将解析JSON并自动生成对应的TS类型

JSON转Golang类型:
  直接输入JSON示例，我将解析JSON并自动生成对应的Golang类型

JSON转Java类:
  直接输入JSON示例，我将解析JSON并自动生成对应的JAVA类

JSON格式化:
  直接输入JSON示例，我将解析JSON并自动格式化

JSON压缩:
  直接输入JSON示例，我将解析JSON并自动压缩

JWT解码为JSON:
  将JWT信息进行解析展开，方便直接看下到Payload中的信息此外，如果还有更多趁手能力需要或推荐，请联系我们。
`;

// 插件列表
export const PluginOptions = [
  {
    label: '@Git',
    value: Plugin.Git,
    desc: '支持创建GitHub、Gitee、GitLab生成Git提交信息。',
    placeholder: '请输入您的问题',
    text: '@Git',
    icon: 'GitIcon',

    prefix: CommandSymbols.at,
    children: [
      {
        label: '插件介绍',
        value: Git.GitIntroduce,
        desc: '',
        placeholder: '无需输入内容, 回车获得插件介绍。',
        text: 'Git 插件介绍',
        content: gitIntroduceContent,
        source: '@' + Plugin.Git,
        parentValue: Plugin.Git,
        prefix: CommandSymbols.at
      },
      {
        label: '生成提交',
        value: Git.GenerateGitCommit,
        desc: '',
        placeholder: '无需输入内容, 回车为您生成提交信息。',
        text: '为当前项目生成Git提交信息',
        content: '',
        source: '@' + Plugin.Git,
        parentValue: Plugin.Git,
        prefix: CommandSymbols.at
      }
    ]
  },
  {
    label: '@工具箱',
    value: Plugin.Toolbox,
    desc: '工具箱能力汇总。',
    placeholder: '请输入您的问题',
    text: '@Toolbox',
    icon: 'ToolboxIcon',
    prefix: CommandSymbols.at,
    children: [
      {
        label: '插件介绍',
        value: Toolbox.ToolboxIntroduce,
        desc: '',
        placeholder: '无需输入内容, 回车获得插件介绍。',
        text: '工具箱 插件介绍',
        content: toolboxIntroduceContent,
        source: '@工具箱',
        parentValue: Plugin.Toolbox,
        prefix: CommandSymbols.at
      },
      {
        label: 'Base64编码',
        value: Toolbox.Base64Encode,
        desc: '',
        placeholder: '请输入或选中需要Base64编码的字符串',
        text: 'Base64编码',
        content: '',
        source: '@工具箱',
        parentValue: Plugin.Toolbox,
        prefix: CommandSymbols.at
      },
      {
        label: 'Base64解码',
        value: Toolbox.Base64Decode,
        desc: '',
        placeholder: '请输入或选中需要解码的Base64字符串',
        text: 'Base64解码',
        content: '',
        source: '@工具箱',
        parentValue: Plugin.Toolbox,
        prefix: CommandSymbols.at
      },
      {
        label: 'MD5编码',
        value: Toolbox.MD5Encode,
        desc: '',
        placeholder: '请输入或选中需编码的字符串',
        text: 'MD5编码',
        content: '',
        source: '@工具箱',
        parentValue: Plugin.Toolbox,
        prefix: CommandSymbols.at
      },
      {
        label: 'URI编码',
        value: Toolbox.URIEncode,
        desc: '',
        placeholder: '请输入或选中需要编码的URI字符串',
        text: 'URI编码',
        content: '',
        source: '@工具箱',
        parentValue: Plugin.Toolbox,
        prefix: CommandSymbols.at
      },
      {
        label: 'URI解码',
        value: Toolbox.URIDecode,
        desc: '',
        placeholder: '请输入或选中需要解码的URI字符串',
        text: 'URI解码',
        content: '',
        source: '@工具箱',
        parentValue: Plugin.Toolbox,
        prefix: CommandSymbols.at
      },
      {
        label: 'JSON转YAML',
        value: Toolbox.JSONToYAML,
        desc: '',
        placeholder: '请输入或选中需转换的JSON',
        text: 'JSON转YAML',
        content: '',
        source: '@工具箱',
        parentValue: Plugin.Toolbox,
        prefix: CommandSymbols.at
      },
      {
        label: 'YAML转JSON',
        value: Toolbox.YAMLToJSON,
        desc: '',
        placeholder: '请输入或选中需转换的YAML字符串',
        text: 'YAML转JSON',
        content: '',
        source: '@工具箱',
        parentValue: Plugin.Toolbox,
        prefix: CommandSymbols.at
      },
      {
        label: 'JSON转TS类型',
        value: Toolbox.JSONToTypeScriptType,
        desc: '',
        placeholder: '请输入或选中需要转为TS类型的JSON字符串',
        text: 'JSON转TS类型',
        content: '',
        source: '@工具箱',
        parentValue: Plugin.Toolbox,
        prefix: CommandSymbols.at
      },
      {
        label: 'JSON转Golang类型',
        value: Toolbox.JSONToGolangType,
        desc: '',
        placeholder: '请输入或选中需要转为Golang类型的JSON字符串',
        text: 'JSON转Golang类型',
        content: '',
        source: '@工具箱',
        parentValue: Plugin.Toolbox,
        prefix: CommandSymbols.at
      },
      {
        label: 'JSON转Java类',
        value: Toolbox.JSONToJavaClass,
        desc: '',
        placeholder: '请输入或选中需要转为Java类的JSON字符串',
        text: 'JSON转Java类',
        content: '',
        source: '@工具箱',
        parentValue: Plugin.Toolbox,
        prefix: CommandSymbols.at
      },
      {
        label: 'JSON格式化',
        value: Toolbox.JSONFormat,
        desc: '',
        placeholder: '请输入或选中需要格式化的JSON字符串',
        text: 'JSON格式化',
        content: '',
        source: '@工具箱',
        parentValue: Plugin.Toolbox,
        prefix: CommandSymbols.at
      },
      {
        label: 'JSON压缩',
        value: Toolbox.JSONCompress,
        desc: '',
        placeholder: '请输入或选中需要压缩的JSON字符串',
        text: 'JSON压缩',
        content: '',
        source: '@工具箱',
        parentValue: Plugin.Toolbox,
        prefix: CommandSymbols.at
      },
      {
        label: 'JWT解码为JSON',
        value: Toolbox.JWTToJSON,
        desc: '',
        placeholder: '请输入或选中需要解码为JSON的JWT字符串',
        text: 'JWT解码为JSON',
        content: '',
        source: '@工具箱',
        parentValue: Plugin.Toolbox,
        prefix: CommandSymbols.at
      },
    ]
  }
];

export const KnowledgeOptions = [
  {
    label: CommandSymbols.hash + '当前代码库',
    value: Directive.ProjectQARepository,
    desc: '',
    placeholder: '请输入您的问题',
    text: '',
    icon: 'CodebaseIcon',
    prefix: CommandSymbols.hash,
    parentValue: 'Knowledge',
  },
  {
    label: CommandSymbols.hash + '目录',
    value: Directive.ProjectQADirectory,
    desc: '',
    placeholder: '请输入您的问题',
    text: '',
    icon: 'FolderIcon',
    prefix: CommandSymbols.hash,
    showSearch: true,
    children: [],
    parentValue: 'Knowledge',
  },
  {
    label: CommandSymbols.hash + '文件',
    value: Directive.ProjectQAFile,
    desc: '',
    placeholder: '请输入您的问题',
    text: '',
    icon: 'FileIcon',
    showSearch: true,
    prefix: CommandSymbols.hash,
    children: [],
    parentValue: 'Knowledge',
  },
  // {
  //   label: CommandSymbols.hash + '知识集',
  //   value: Directive.KnowledgeBase,
  //   desc: '',
  //   placeholder: '请输入您的问题',
  //   text: '',
  //   icon: 'FolderIcon',
  //   showSearch: true,
  //   prefix: CommandSymbols.hash,
  //   children: [],
  //   parentValue: 'Knowledge',
  // }
]

export const AllExtendOptions = [
  {
    label: '/指令',
    value: 'Directive',
    children: DirectiveOptions,
    prefix: CommandSymbols.slash
  },
  {
    label: '@插件',
    value: 'Plugin',
    children: PluginOptions,
    prefix: CommandSymbols.at
  },
  {
    label: '#知识',
    value: 'Knowledge',
    children: KnowledgeOptions,
    prefix: CommandSymbols.hash
  },
];

export const TerminalLanguage = ['shell', 'bash'];

export const PluginIntroduceList = [
  Git.GitIntroduce,
  Toolbox.ToolboxIntroduce,
]


// 智能体
export const AgentContextOptions = [
  {
    label: CommandSymbols.hash + '目录',
    value: Directive.ProjectQADirectory,
    desc: '',
    placeholder: '请输入您的问题',
    text: '',
    icon: 'FolderIcon',
    prefix: CommandSymbols.hash,
    showSearch: true,
    children: [],
    parentValue: 'Context',
  },
  {
    label: CommandSymbols.hash + '文件',
    value: Directive.ProjectQAFile,
    desc: '',
    placeholder: '请输入您的问题',
    text: '',
    icon: 'FileIcon',
    showSearch: true,
    prefix: CommandSymbols.hash,
    children: [],
    parentValue: 'Context',
  }
]


export const AgentAllExtendOptions = [
  {
    label: '添加上下文',
    value: 'Context',
    children: AgentContextOptions,
    prefix: CommandSymbols.add,
    icon: 'AddIcon',
  },
]