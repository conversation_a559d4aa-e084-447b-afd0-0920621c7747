.mcp-details {
  background-color: rgba(0,0,0,0.2);
  padding: 8px 10px;
  margin: 4px 0;
  border-radius: 4px;
}

.mcp-details-content{
  width: 100%;
  overflow-x: auto;
}

.mcp-tool-response{
  background-color: rgba(0,0,0,0.2);
  padding: 8px 10px;
  margin: 4px 0;
  border-radius: 4px;
}

.tool-call-item{
  padding: 0 6px;
  border: 1px solid var(--panel-border-color); 
  margin: 4px 0;

  &_content{
    height: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left{
      display: flex;
       flex: 1;
      align-items: center;
    }

    .right{
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }

  &_type{
    padding-left: 2px;

    &_tag{
      padding:0 4px;
      font-size: 11px;
      color: var(--panel-text-color);
      background-color: var(--card-bg-hover); 
    }
  }

  &_info{
    display: flex;
    align-items: center;
    padding-left: 6px;
    height: 20px;

    .file-icon-wrapper{
      svg {
        display: flex;
        align-items: center;
      }
    }

    .file-name{
      display: flex;
      align-items: center;
      height: 100%;
    }
  }

  &_action{
    font-size: 11px;
    display: flex;
    align-items: center;
    gap: 8px;
    
    &_content {
      opacity: 0.8;
    }

    &_tag{
      display: flex;
      align-items: center;
      padding-left: 4px;

      .label {
        padding-left: 2px;
      }

      .add-line{
        display: inline-flex;
        color: rgb(112 217 140 / 1)
      }
      .del-line{
        display: inline-flex;
        color: rgb(229 69 82 / 1);
      }    

      .link-item {
        cursor: pointer;
        color: var(--vscode-textLink-activeForeground);
        margin-left: 8px;
      }
    }
  }
}