import { CloseCircleOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { Spin } from 'antd';

import './index.less'
export const AcceptedButton = (props: { btnText?: string }) => {
  const { btnText = "已接受" } = props;
  return (
    <div className='action-button accepted'>
      <CheckCircleOutlined />
      <span className='label'>{btnText}</span>
    </div>
  )
}
export const RejectedButton = (props: { btnText?: string }) => {
  const { btnText = "已拒绝" } = props;

  return (
    <div className='action-button ejected'>
      <CloseCircleOutlined />
      <span className='label'>{btnText}</span>
    </div>
  )
}
export const AppliedButton = (props: { btnText?: string }) => {
  const { btnText = "已应用" } = props;

  return (
    <div className='action-button applied'>
      <CheckCircleOutlined />
      <span className='label'>{btnText}</span>
    </div>
  )
}

export const CreatingButton = (props: { btnText?: string }) => {
  const { btnText = "生成中" } = props;

  return (
    <div className='action-button creating'>
      <Spin size='small' />
      <span className='label'>{btnText}</span>
    </div>
  )
}


