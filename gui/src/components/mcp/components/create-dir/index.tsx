import FileIcon from '@/components/file-icon';
import { Spin } from 'antd';
import { CheckOutlined } from '@ant-design/icons';
import { getFileNameByPath } from 'core/utils/file'
import { useChatContext } from '@/context/chat';
import { useToolCall } from '@/hooks'

import '../../mcp.less'
export const CreateDir = (props) => {
  const { args, id: toolCallId, messageId } = props || {}
  const { getToolCallStatus, getToolCache } = useToolCall();
  const { messages } = useChatContext();

  const fileName = getFileNameByPath(args?.path)
  const status = getToolCallStatus(messages, messageId, toolCallId);

  const showLoading = getToolCache(toolCallId)?.isPending
  const isSuccess = status === 'success'

  return (
    <div className="tool-call-item" >
      <div className='tool-call-item_content'>
        <div className='left'>
          <div className="tool-call-item_type">
            <div className="tool-call-item_type_tag">
              创建目录
            </div>
          </div>
          <div className="tool-call-item_info">
            <FileIcon filename={fileName} width="20px" height="20px" />
            <span className="file-name">{fileName} </span>
          </div>
        </div>

        <div className='right'>
          <div className='tool-call-item_action'>
            {
              showLoading && !isSuccess &&
              <div className='tool-call-item_action_tag'>
                <Spin tip="Loading" size='small' />
                <span className='label'>创建中...</span>
              </div>
            }

            {
              isSuccess &&
              <div className='tool-call-item_action_tag'>
                <CheckOutlined style={{ color: "green" }} />
              </div>
            }
          </div>
        </div>
      </div>
    </div>
  );
}