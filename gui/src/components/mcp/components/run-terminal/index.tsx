
import { Button, Input, Spin } from 'antd';
import { onToolCallCancel, onToolCallOk } from '@/utils/mcp'
import { toolCacheManager } from '@/utils/mcp-tools-cache';
import { useEffect, useState } from 'react';
import { CheckOutlined } from '@ant-design/icons';
import { useChatContext } from '@/context/chat';
import { useToolCall } from '@/hooks'

import './index.less'
export const RunTerminalCommand = (props) => {
  const { args, id: toolCallId, messageId } = props || {}
  const [_isLoading, setIsLoading] = useState(false);
  const { messages } = useChatContext();
  const { getToolCallStatus, getToolCache, getToolCacheResult } = useToolCall();

  const toolCache = getToolCache(toolCallId);
  const status = getToolCallStatus(messages, messageId, toolCallId);

  const isSuccess = (status === 'success') || getToolCacheResult(toolCallId)?.status === 'success';

  const showCmdBtn = !isSuccess && !toolCache?.isTriggered

  const run = async () => {
    toolCacheManager.onToolCall(toolCallId);
  }

  const onRunCommand = () => {
    setIsLoading(true);
    onToolCallOk(props)
    run();
  }
  const onCancelCommand = () => {
    onToolCallCancel(props)
    run();
  }

  useEffect(() => {
    console.log('status===>', status);
  }, [status]);

  return (
    <div className="tool-call-item_terminal">
      <div className="title">终端运行</div>
      <div className="line">
        <div className='status'>
          {toolCache?.isLoading && <Spin size="small" />}
          {isSuccess && <CheckOutlined style={{ color: "green" }} />}
        </div>
        <Input defaultValue={args.command} readOnly autoFocus />
      </div>
      {
        showCmdBtn &&
        <div className="actions">
          <Button
            type='primary'
            size='small'
            className="mcp-terminal-run-btn"
            onClick={onRunCommand}
          >
            运行
          </Button>
          <Button className="mcp-terminal-cancel-btn" onClick={onCancelCommand}>取消</Button>
        </div>
      }
    </div >
  );
}