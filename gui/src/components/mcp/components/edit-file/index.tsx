import FileIcon from '@/components/file-icon';
import { useContext } from 'react'
import { IdeMessengerContext } from '@/context/ide-messenger'
import { getFileNameByPath } from 'core/utils/file';
import { useToolCall } from '@/hooks'
import { useChatContext } from '@/context/chat';
import { AcceptedButton, RejectedButton } from '../action-buttons';
import { CheckOutlined } from '@ant-design/icons';

import '../../mcp.less'

export const EditFile = (props) => {
  const { args, id: toolCallId, messageId } = props || {}
  const { messages } = useChatContext();
  const ideMessenger = useContext(IdeMessengerContext);
  const {
    getToolCache,
    getHistoryDiff,
    getToolCallStatus
  } = useToolCall()
  const historyDiff = getHistoryDiff(messages, messageId, toolCallId)
  const toolCache = getToolCache(toolCallId);
  const fileName = getFileNameByPath(args?.path || '')

  // 调用状态
  const status = getToolCallStatus(messages, messageId, toolCallId);
  const isSuccess = status === 'success'

  const statusSet = new Set([toolCache?.diff?.status, historyDiff?.status]);

  const isAccepted = statusSet.has('accepted');
  const isRejected = statusSet.has('rejected');

  const onClickFile = () => {
    ideMessenger.post('file/open', { path: args?.path })
  }

  return (
    <div className="tool-call-item">
      <div className="tool-call-item_content">
        <div className="left">
          <div className="tool-call-item_type">
            <div className="tool-call-item_type_tag">
              {toolCache?.isPending ? '分析' : '编辑'}
            </div>
          </div>
          <div className="tool-call-item_info" onClick={onClickFile}>
            <FileIcon filename={fileName} width="20px" height="20px" />
            <span className="file-name">{fileName} </span>
          </div>
        </div>
        <div className="right">
          <div className="tool-call-item_action">
            <div className='tool-call-item_action_tag'>

              {isAccepted && <AcceptedButton />}
              {isRejected && <RejectedButton />}

              {!isAccepted && !isRejected && isSuccess &&
                <div className='tool-call-item_action_tag'>
                  <CheckOutlined style={{ color: "green" }} />
                </div>
              }
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}