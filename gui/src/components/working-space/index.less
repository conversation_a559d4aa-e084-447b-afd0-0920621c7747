.light-theme .workspace{
  --workspace-text-color: #333;
  --workspace-file-item-hove-bg-color: #e8e8e8;
}

.dark-theme .workspace{
  --workspace-text-color: #ccc;
  --workspace-file-item-hove-bg-color: #373737;
}

.workspace {
  margin: 0 10px 6px;
  font-size: 12px;
  color:var(--workspace-text-color);
  &-container {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }

  &-header {
    height: 32px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    padding: 0 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow-x: auto;
    background-color:var(--chat-input-background);
    cursor: pointer;

    &-left{
      .title{
        cursor: pointer;
        font-size: 13px;
        
        .icon{
          font-size: 12px;
        }

        .label {
          padding-left: 4px;
        }
      }
    }

    &-right {
      display: flex;
      align-items: center;
      
      .action-button{
        padding-left: 4px;
      }
    }

    &-text-button{
      padding-left: 6px;
      cursor: pointer; 
      color: var(--vscode-textLink-activeForeground);
    }
  }

  &-content{
    padding: 4px 0;
    background-color: rgba(0, 0, 0, .04);
    max-height: 200px;
    margin-top: 2px;
    overflow-y: auto;
    background-color: var(--chat-input-background);    
    cursor: pointer;

    &-file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 8px;
      margin: 2px 4px;
      height: 24px;

      &-status {
        display: flex;
        align-items: center;

        &-text {
          display: inline-flex;
          font-size: 12px;
          color: #81b88b;
        }

        &-icon {
          margin-right: 4px;
          font-size: 12px;
          transform: scale(1);
          color: #89d185;
        }
        .add{
          color: #4caf50;
          margin-right: 4px;
        } 
        .delete{
          color: #f44336;
          margin-right: 8px;
        }
      }

      &-operation{
        display: none;  
      }

      &:hover {
        background-color:var(--workspace-file-item-hove-bg-color);
      }

      &:hover &-operation {
        display: flex;
      }

      &:hover &-status {
        display: none;
      }

      &-left, &-right {
        display: flex;
        align-items: center;
      }
    }
  }
}