
import { useEffect, useState } from 'react'
import { DownOutlined, RightOutlined } from '@ant-design/icons';
import FileIcon from '@/components/file-icon';
import { getFileNameByPath } from 'core/utils/file';
import { toolCacheManager, McpToolsCacheItem } from '@/utils/mcp-tools-cache'
import { useToolCall, useWebviewListener } from '@/hooks'
import { useChatContext } from '@/context/chat';
import { getMessagesToolDiffById } from '@/pages/agent/utils'
import { historyManager } from '@/utils/history';
import { AcceptedButton, RejectedButton, AppliedButton, CreatingButton } from '../mcp/components/action-buttons';

import './index.less'

const DiffActions = ['rejected', 'accepted']

/**
 * 过滤缓存列表：路径和toolName相同的项，只保留最后出现的（最新的）
 * @param caches 缓存数组（顺序越靠后越新）
 * @returns 去重后的缓存列表
 */
const filterCacheList = (caches: McpToolsCacheItem[]): McpToolsCacheItem[] => {
  if (caches.length === 0) return [];

  // 用对象记录每组最新的项（键：path::toolName，值：最新缓存项）
  const latestCacheMap = {};

  caches.forEach(cache => {
    // 获取路径和工具名（处理可能的空值）
    const path = cache.toolCall.args?.path ?? '';
    const toolName = cache.toolCall.name ?? '';
    // 生成唯一分组键
    const groupKey = `${path}::${toolName}`;

    // 直接覆盖更新：后出现的项会替换之前的（因为数组后元素更新）
    latestCacheMap[groupKey] = cache;
  });

  // 从对象.values()获取结果（顺序不保证，但都是每组最新的）
  return Object.values(latestCacheMap);
};

export const WorkingSpace = (props: {
  minimize?: boolean,
  setMinimize?: (minimize: boolean) => void
}) => {
  const { minimize, setMinimize } = props;
  const [showPanel, setShowPanel] = useState(true);
  const {
    fileShowDiff,
    openFile,
    acceptFileDiff,
    rejectFileDiff,
    editFileList
  } = useToolCall();
  const { messages, setMessages, messageIds } = useChatContext();

  const cacheList = filterCacheList(editFileList)
  console.log('cacheList===>', cacheList);

  // 显示loading
  const hasLoading = cacheList.some(item => item?.toolResult?.status !== 'success')
  // 全部工具执行成功
  const allSuccess = cacheList.every(item => item?.toolResult?.status === 'success')

  const allRejected = cacheList.every(item => item?.diff?.status === 'rejected')
  const allAccepted = cacheList.every(item => item?.diff?.status === 'accepted')

  // 部分接受
  const hasPartAccepted = cacheList.every(i => DiffActions.includes(i?.diff?.status))
    && cacheList.some(i => i?.diff?.status === 'accepted')

  // 未操作
  const hasUntreated = cacheList.some(i => !DiffActions.includes(i.diff?.status))

  const setCacheDiffState = async (
    toolCallId: string,
    path: string,
    status: 'accepted' | 'rejected' | 'pending',
    diffResult?: { added: number; deleted: number }
  ) => {

    toolCacheManager.setToolCallDiffInfo(toolCallId, {
      path,
      status
    })

    let added = 0, deleted = 0;

    if (diffResult) {
      added = diffResult.added
      deleted = diffResult.deleted
    }

    const list = getMessagesToolDiffById(messages, messageIds?.messageId, toolCallId, {
      path,
      status: status,
      id: toolCallId,
      added,
      deleted,
    })

    setMessages(list)
    historyManager.save(list)
  }

  const onAcceptFile = (cache: McpToolsCacheItem) => {
    const { id, args } = cache?.toolCall || {}

    const diffInfo = {
      added: cache?.diff?.added,
      deleted: cache?.diff?.deleted,
    }

    acceptFileDiff(args?.path)
    setCacheDiffState(id, args?.path, 'accepted', diffInfo)
  }
  const onRejectFile = (cache: McpToolsCacheItem) => {
    const { id, args } = cache?.toolCall || {}
    const diffInfo = {
      added: cache?.diff?.added,
      deleted: cache?.diff?.deleted,
    }
    rejectFileDiff(args?.path)
    setCacheDiffState(id, args?.path, 'rejected', diffInfo)
  }

  // 采纳所有
  const onAcceptAllFile = (caches: McpToolsCacheItem[]) => {
    const list = caches.filter(i => !['rejected', 'accepted'].includes(i.diff?.status))
    list.forEach(i => {
      onAcceptFile(i)
    })
  }

  // 放弃所有
  const onRejectAllFile = (caches: McpToolsCacheItem[]) => {
    const list = caches.filter(i => !['rejected', 'accepted'].includes(i.diff?.status))
    list.forEach(i => {
      onRejectFile(i)
    })
  }

  // 查看所有变更
  const onViewAllDiff = (caches: McpToolsCacheItem[]) => {

    try {
      caches.forEach(i => {
        const result = i?.toolResult ? JSON.parse(i?.toolResult?.result) : null
        const path = i.toolCall?.args?.path
        const content = result?.content?.[0]?.text

        if (path && content) {
          fileShowDiff(path, content)
        }
      })
    } catch (error) {
      console.log('onViewAllDiff', error);
    }
  }

  useWebviewListener({
    "file/acceptDiff": ({ payload }) => {
      const item = cacheList?.find(item =>
        item.toolCall?.args?.path === payload?.path &&
        !['rejected', 'accepted'].includes(item.diff?.status)
      )
      if (item) {
        onAcceptFile(item)
      }
    }
  }, [cacheList])

  useWebviewListener({
    "file/rejectDiff": ({ payload }) => {
      const item = cacheList?.find(item =>
        item.toolCall?.args?.path === payload?.path &&
        !['rejected', 'accepted'].includes(item.diff?.status)
      )
      if (item) {
        onRejectFile(item)
      }
    }
  }, [cacheList])

  useEffect(() => {
    setShowPanel(!minimize)
  }, [minimize])


  return (
    <div className='workspace' >
      <div className='workspace-container'>
        <div className='workspace-header' onClick={() => {
          setMinimize && setMinimize(!minimize)
          setShowPanel(!showPanel)
        }}>
          <div className="workspace-header-left">
            <div className='title'>
              <span className='icon'>
                {showPanel ? <DownOutlined /> : <RightOutlined />}
              </span>
              <span className='label'>工作区</span>
            </div>
          </div>
          <div className='workspace-header-right'>

            {
              hasLoading ? <CreatingButton />
                :
                <>
                  {!allAccepted && !allRejected && allSuccess && <AppliedButton />}

                  {allAccepted && <AcceptedButton />}
                  {allRejected && <RejectedButton />}

                  {!allAccepted && !allAccepted && hasPartAccepted && <AcceptedButton btnText='部分接受' />}
                </>
            }

            {
              hasUntreated &&
              <>
                <div
                  className='workspace-header-text-button'
                  onClick={(e) => {
                    e.stopPropagation()
                    onViewAllDiff(cacheList)
                  }}
                >查看变更</div>

                <div
                  className='workspace-header-text-button'
                  onClick={(e) => {
                    e.stopPropagation()
                    onAcceptAllFile(cacheList)
                  }}
                >接受</div>

                <div className='workspace-header-text-button'
                  onClick={(e) => {
                    e.stopPropagation()
                    onRejectAllFile(cacheList)
                  }}
                >拒绝</div>
              </>
            }
          </div>
        </div>
        {
          showPanel &&
          <div className='workspace-content'>
            {
              cacheList?.map((i) => {
                const path = i.toolCall?.args?.path || ''
                const fileName = getFileNameByPath(path)
                const toolResult = i?.toolResult?.result ? JSON.parse(i.toolResult.result) : null
                const diffInfo = toolResult ? toolResult.content[0]?.extraData : {}

                const isToolSuc = i?.toolResult?.status === 'success'

                const isAccepted = i?.diff?.status === 'accepted'
                const isRejected = i?.diff?.status === 'rejected'
                const showActions = !isAccepted && !isRejected && isToolSuc

                return (
                  <div className='workspace-content-file-item' onClick={() => openFile(path)}>
                    <div className='workspace-content-file-item-left'>
                      <FileIcon filename={fileName} width="20px" height="20px" />
                      <span>{fileName} </span>
                    </div>
                    <div className='workspace-content-file-item-right'>
                      {
                        showActions &&
                        <>
                          <div className='workspace-content-file-item-status'>
                            <span className='workspace-content-file-item-status-text add'>+{diffInfo?.added || 0}</span>
                            <span className='workspace-content-file-item-status-text delete'>-{diffInfo?.deleted || 0}</span>
                          </div>
                          <div className='workspace-content-file-item-operation'>
                            <div className='workspace-header-text-button' onClick={() => onRejectFile(i)}>拒绝</div>
                            <div className='workspace-header-text-button' onClick={() => onAcceptFile(i)}>接受</div>
                          </div>
                        </>
                      }

                      {
                        isToolSuc ?
                          <>
                            {isAccepted && <AcceptedButton />}
                            {isRejected && <RejectedButton />}
                          </>
                          : <CreatingButton />
                      }
                    </div>
                  </div>
                )
              })
            }
          </div>
        }
      </div>
    </div >
  )
}