import { CommandItem } from '@/types/command';
import cx from 'classnames';
import { useEffect, useState, useMemo } from 'react';
import { useMemoizedFn } from 'ahooks';
import * as Icons from '@/components/icons';
import { SearchOutlined, SyncOutlined, LeftOutlined } from '@ant-design/icons'
import { Input } from 'antd';
import FileIcon from '../file-icon';
import { Empty } from 'antd';

import './index.less';

interface CommandListProps {
  onChange: (item: CommandItem) => void;
  onSearch?: (searchText: string) => void;
  onRefresh?: () => void;
  onReturn?: () => void;
  options: CommandItem[];
  value: string;
  showSearch?: boolean;
  showHeader?: boolean;
  showFileIcon?: boolean;
  title?: string;
  showDescOnHover?: boolean;
  loading?: boolean;
  placeholder?: string;
}


export function renderIcon(iconName: string): JSX.Element | null {
  const iconComponent = Icons[iconName as keyof typeof Icons];
  return iconComponent ? iconComponent() : null;
}

/**
 * 命令列表组件
 *
 * @param props 组件属性
 * @param props.onChange 当选中项发生变化时的回调函数
 * @param props.options 命令列表数据
 * @param props.value 当前选中项的值
 * @returns 命令列表组件
 */
export const CommandList: React.FC<CommandListProps> = (props) => {
  const { onChange,
    onSearch,
    onRefresh,
    options,
    value,
    showFileIcon = false,
    showSearch = false,
    showDescOnHover = false,
    showHeader = false,
    title = '',
    loading = false,
    onReturn,
    placeholder = '输入关键字搜索',
  } = props || {};
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const [curtOptions, setCurtOptions] = useState<CommandItem[]>(options);

  const titleRemovePrefix = useMemo(() => {
    return title.replace(/^(#|@)/, '');
  }, [title])

  useEffect(() => {
    setCurtOptions(options || []);
    if (value) {
      setActiveIndex(options.findIndex((i: CommandItem) => i.value === value));
    }
  }, [options, value]);

  // 重置当前选项列表
  useEffect(() => {
    setActiveIndex(0)
  }, [options])

  const onCommandItemClick = (item: CommandItem, index: number) => {
    setActiveIndex(index);
    onChange && onChange(item);
  };

  const onSearchHandle = (searchValue: string) => {
    onSearch && onSearch(searchValue);
  }

  /**
   * 处理键盘按下事件
   *
   * @param event 键盘事件对象
   */
  const handleKeyDown = useMemoizedFn((event: KeyboardEvent) => {
    const { keyCode } = event;
    const isUpKey = keyCode === 38; // 上键
    const isDownKey = keyCode === 40; // 下键
    const isEnterKey = keyCode === 13; // 回车键

    if (isUpKey) {
      // 上键：如果已经是第一项，则不处理
      if (activeIndex > 0) {
        setActiveIndex((prevIndex) => prevIndex - 1);
      }
    } else if (isDownKey) {
      // 下键：如果已经是最后一项，则不处理
      if (activeIndex < curtOptions.length - 1) {
        setActiveIndex((prevIndex) => prevIndex + 1);
      }
    } else if (isEnterKey) {
      // 回车键：选择当前项
      const selectedItem = curtOptions?.find((_, idx) => idx === activeIndex);
      if (selectedItem) {
        onChange?.(selectedItem as CommandItem);
        event.preventDefault();
      }
    }
  });

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [value, activeIndex, options]);

  const getCommandOptions = useMemoizedFn(() => {
    return curtOptions?.length > 0 ?
      <ul>
        {curtOptions?.map((i: CommandItem, index: number) => (
          <li
            className={cx('command-item', {
              active: activeIndex === index,
              hoverDesc: showDescOnHover,
              'show-desc': !i?.source,
            })}
            key={i?.id || i.value}
            onClick={() => onCommandItemClick(i, index)}
          >
            <div className='command-info'>
              {i.icon && <span className='icon'>{renderIcon(i.icon)}</span>}
              {i.label && showFileIcon && <FileIcon filename={i.label} width="20px" height="20px" />}
              <span className='text-ellipsis'>{i.label}</span>
            </div>
            <div
              className="command-desc text-ellipsis"
              title={!i?.source && i?.desc ? i.desc : ''}
            >{i?.source ? i.source : i.desc}</div>
          </li>
        ))}
      </ul>
      : <div className='no-data'><Empty description="暂无数据" /></div>
  })

  return (
    <div className='command-list'>
      {
        showHeader &&
        <div className='header'>
          <div className='title' onClick={() => onReturn && onReturn()}>
            {onReturn && <span className='return-icon'><LeftOutlined /></span>}
            <span>{titleRemovePrefix}</span>
          </div>
          <div className='action-box'>
            <div className='action-item' onClick={() => onRefresh && onRefresh()}>
              <SyncOutlined />
            </div>
          </div>
        </div>
      }
      {
        showSearch &&
        <div className='search-box'>
          <Input
            addonBefore={<SearchOutlined />}
            allowClear
            placeholder={placeholder}
            onChange={(e) => onSearchHandle(e.target.value)}
          />
        </div>
      }
      {
        getCommandOptions()
      }
    </div>
  );
};
