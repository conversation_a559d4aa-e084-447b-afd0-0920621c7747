.command-list {
  outline: none;
  font-size: 13px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px 0;

    .title {
      font-size: 14px;
      font-weight: bold;
      display: flex;
      align-items: center;
      gap: 4px;
      cursor: pointer;

      .return-icon {
        font-size: 12px;
        opacity: 0.8;
        cursor: pointer;
      }
    }

    .action-box {
      display: flex;

      .action-item {
        cursor: pointer;
      }
    }
  }

  .search-box {
    padding: 10px 12px 0;
  }

  ul {
    max-height: 50vh;
    overflow-y: auto;
    list-style: none;
    padding: 10px 12px;
    margin: 0;

    .command-item {
      height: 20px;
      padding: 4px 8px;
      display: flex;
      justify-content: space-between;
      gap: 10px;
      align-items: center;
      flex: 4;
      cursor: pointer;
      color: var(--command-item-color);
      border-radius: 6px;

      &:hover {
        background-color: var(--command-item-hover-background);
      }

      .command-info {
        display: flex;
        align-items: center;
        flex: 1;
        // 允许收缩
        min-width: 0;

        .icon {
          display: flex;
          align-items: center;
          padding-right: 4px;
        }
      }
    }


    .command-desc {
      text-align: right;
    }

    .hoverDesc {
      .command-desc {
        display: none;
      }

      &:hover {
        .command-desc {
          display: block;
        }
      }
    }

    .active {
      background-color: var(--command-item-active-background);
    }

    .show-desc {
      justify-content: flex-start;

      .command-info {
        flex: initial;
      }

      .command-desc {
        color: #b0b0b0;
        font-size: 12px;
        flex: 1;
        text-align: left;
      }
    }
  }

  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px;
  }
}