// import fs from "fs/promises";
import { diffLines, createTwoFilesPatch } from 'diff';


// 简单的diff应用，仅支持单文件、无hunk定位的增删
export function getGitDiffStats(originalContent: string, diff: string): { added: number; deleted: number } {
  const lines = originalContent.split('\n');
  const diffLines = diff.split('\n');
  let i = 0;
  let added = 0;
  let deleted = 0;
  for (let d = 0; d < diffLines.length; d++) {
    const line = diffLines[d];
    if (line.startsWith('---') || line.startsWith('+++') || line.startsWith('diff')) continue;
    if (line.startsWith('-')) {
      i++;
      deleted++;
    } else if (line.startsWith('+')) {
      added++;
    } else if (line.startsWith('@@')) {
      continue;
    } else {
      if (i < lines.length) {
        i++;
      }
    }
  }
  return { added, deleted };
}

export function applyGitDiffToContent(originalContent: string, diff: string): string {
  const lines = originalContent.split('\n');
  const diffLines = diff.split('\n');
  let result: string[] = [];
  let i = 0;
  for (let d = 0; d < diffLines.length; d++) {
    const line = diffLines[d];
    if (line.startsWith('---') || line.startsWith('+++') || line.startsWith('diff')) continue;
    if (line.startsWith('-')) {
      i++;
    } else if (line.startsWith('+')) {
      result.push(line.slice(1));
    } else if (line.startsWith('@@')) {
      continue;
    } else {
      if (i < lines.length) {
        result.push(lines[i]);
        i++;
      }
    }
  }
  while (i < lines.length) {
    result.push(lines[i]);
    i++;
  }
  return result.join('\n');
}


// file editing and diffing utilities
function normalizeLineEndings(text: string): string {
  return text.replace(/\r\n/g, '\n');
}


function createUnifiedDiff(originalContent: string, newContent: string, filepath: string = 'file'): string {
  // Ensure consistent line endings for diff
  const normalizedOriginal = normalizeLineEndings(originalContent);
  const normalizedNew = normalizeLineEndings(newContent);

  return createTwoFilesPatch(
    filepath,
    filepath,
    normalizedOriginal,
    normalizedNew,
    'original',
    'modified'
  );
}


export async function applyFileEdits(
  originalContent: string,
  filePath: string,
  edits: Array<{ oldText: string, newText: string }>,
): Promise<any> {
  // Read file content and normalize line endings
  const content = normalizeLineEndings(originalContent);

  // Apply edits sequentially
  let modifiedContent = content;
  for (const edit of edits) {
    const normalizedOld = normalizeLineEndings(edit.oldText);
    const normalizedNew = normalizeLineEndings(edit.newText);

    // If exact match exists, use it
    if (modifiedContent.includes(normalizedOld)) {
      modifiedContent = modifiedContent.replace(normalizedOld, normalizedNew);
      continue;
    }

    // Otherwise, try line-by-line matching with flexibility for whitespace
    const oldLines = normalizedOld.split('\n');
    const contentLines = modifiedContent.split('\n');
    let matchFound = false;

    for (let i = 0; i <= contentLines.length - oldLines.length; i++) {
      const potentialMatch = contentLines.slice(i, i + oldLines.length);

      // Compare lines with normalized whitespace
      const isMatch = oldLines.every((oldLine, j) => {
        const contentLine = potentialMatch[j];
        return oldLine.trim() === contentLine.trim();
      });

      if (isMatch) {
        // Preserve original indentation of first line
        const originalIndent = contentLines[i].match(/^\s*/)?.[0] || '';
        const newLines = normalizedNew.split('\n').map((line, j) => {
          if (j === 0) return originalIndent + line.trimStart();
          // For subsequent lines, try to preserve relative indentation
          const oldIndent = oldLines[j]?.match(/^\s*/)?.[0] || '';
          const newIndent = line.match(/^\s*/)?.[0] || '';
          if (oldIndent && newIndent) {
            const relativeIndent = newIndent.length - oldIndent.length;
            return originalIndent + ' '.repeat(Math.max(0, relativeIndent)) + line.trimStart();
          }
          return line;
        });

        contentLines.splice(i, oldLines.length, ...newLines);
        modifiedContent = contentLines.join('\n');
        matchFound = true;
        break;
      }
    }

    if (!matchFound) {
      throw new Error(`Could not find exact match for edit:\n${edit.oldText}`);
    }
  }

  // Create unified diff
  const diff = createUnifiedDiff(content, modifiedContent, filePath);

  // Format diff with appropriate number of backticks
  let numBackticks = 3;
  while (diff.includes('`'.repeat(numBackticks))) {
    numBackticks++;
  }
  const formattedDiff = `${'`'.repeat(numBackticks)}diff\n${diff}${'`'.repeat(numBackticks)}\n\n`;

  return {
    content: modifiedContent,
    diff: formattedDiff,
  }
}