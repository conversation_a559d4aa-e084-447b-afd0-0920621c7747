import { AIMessageChunk, HumanMessage } from "@langchain/core/messages";
import { MessageFactory, ToolCallStatus } from "../stream/index";
import { ToolMessage } from "@langchain/core/messages";
import { writeMessageLog } from "./write_log";

/**
 * Stream转换模块
 * 将输入流中的chunk逐个转换，返回新的异步生成器
 */
export async function* transformStream(
  inputStream: AsyncIterable<any>,
  transformer?: (chunk: any, metaData: any) => any,
  metaData?: any
): AsyncGenerator<any, void, unknown> {
  for await (const chunk of inputStream) {
    writeMessageLog(chunk);
    // 如果提供了转换函数，使用它；否则原样返回
    const transformedChunk = transformer ? transformer(chunk, metaData) : chunk;
    if (!transformedChunk) {
      continue;
    }
    writeMessageLog(transformedChunk, "/tmp/transformed-message.txt");
    yield transformedChunk;
  }
}

/**
 * 默认转换函数
 */
export function defaultTransformer(chunk: any, metaData: any): any {
  if (chunk[0] === "updates" && (chunk[1]?.start?.messages?.[0] instanceof HumanMessage)) {
    return MessageFactory.createHumanMessage(
      metaData,
      chunk[1].start.messages[0].text,
    );
  }

  if (chunk[0] === "messages" && chunk[1]?.length && chunk[1][0] instanceof AIMessageChunk) {
    const content = chunk[1][0].content;
    const tool_call_chunk = chunk[1][0]?.tool_call_chunks?.[0];
    if (!(content || tool_call_chunk)) {
      return null;
    }
    return MessageFactory.createAgentChunkResponseMessage(
      metaData,
      content? content as string : undefined,
      tool_call_chunk? {
        index: tool_call_chunk?.index,
        id: tool_call_chunk?.id,
        type: tool_call_chunk?.type,
        name: tool_call_chunk?.name,
        args: tool_call_chunk?.args
      } : undefined
    );
  }

  if (chunk[0] === "updates" && (chunk[1]?.final)) {
    return MessageFactory.createMessageEndMessage(
      metaData,
    );
  }

  if (chunk[0] === "updates" && chunk[1].agent 
        && chunk[1].agent.messages[0] instanceof AIMessageChunk 
        && chunk[1].agent.messages[0].tool_calls?.length) {
    const toolCall = chunk[1].agent.messages[0].tool_calls?.[0];
    return MessageFactory.createToolCallRequestMessage(
      metaData,
      {
        ...toolCall
      }
    );
  }

  if (chunk[0] === "updates" && chunk[1].tools?.messages[0] instanceof ToolMessage) {
    const toolCall = chunk[1].tools.messages[0];
    return MessageFactory.createToolCallResultMessage(
      metaData,
      {
        id: toolCall?.tool_call_id,
        name: toolCall?.name as string,
        status: ToolCallStatus.SUCCESS,
        result: toolCall?.content as string
      }
    );
  }

  return null;
}