import { AIMessage, SystemMessage } from "@langchain/core/messages";
import { MessagesAnnotation, StateGraph, CompiledStateGraph } from "@langchain/langgraph";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { StructuredTool } from "@langchain/core/tools";
import { GraphProvider } from "../interfaces";
import { SystemPromptProvider } from "../interfaces";
import { ModelProvider } from "../interfaces";
import { ToolProvider } from "../interfaces";
import { CheckpointProvider } from "../interfaces";
import { HumanInterventionProvider } from "../interfaces";
import { addHumanInTheLoopToTools } from "../utils/toolInterceptor";

/**
 * 内置图编排提供者
 * 使用现有的图编排逻辑
 */
export class BuiltinGraphProvider implements GraphProvider {
  async createGraph(
      modelProvider: ModelProvider,
      toolsProvider: ToolProvider,
      systemPromptProvider: SystemPromptProvider,
      checkpointProvider: CheckpointProvider,
      humanInterventionProvider: HumanInterventionProvider
  ): Promise<CompiledStateGraph<any, any, any, any, any, any>> {
    
    const model = await modelProvider.getModel();
    const originalTools = await toolsProvider.getTools();
    const checkpointer = await checkpointProvider.getCheckpointer();
    const humanConfig = await humanInterventionProvider.getHumanInterventionConfig();

    // 根据配置决定是否添加人类介入
    let finalTools = originalTools;
    if (humanConfig.enabled) {
      finalTools = addHumanInTheLoopToTools(originalTools, {
        defaultDescription: humanConfig.defaultDescription || "请确认工具调用",
        toolConfigs: humanConfig.toolConfigs || {},
      });
    }

    const boundModel = model.bindTools!(finalTools);
    const toolNode = new ToolNode(finalTools);

    const shouldContinue = async (state: typeof MessagesAnnotation.State) => {
      const messages = state.messages;
      const lastMessage: AIMessage = messages[messages.length - 1];
      // If the LLM makes a tool call, then we route to the "tools" node
      if (lastMessage.tool_calls?.length) {
        return "tools";
      }
      // Otherwise, we stop (reply to the user)
      return "final";
    };

    const systemPrompt = await systemPromptProvider.getSystemPrompt();

    const callModel = async (state: typeof MessagesAnnotation.State) => {
      const messages = state.messages.filter((msg: any) => msg.constructor.name !== 'ChatMessageChunk');

      const systemMessage = new SystemMessage(systemPrompt);

      // 将系统消息添加到消息列表的开头（如果还没有系统消息的话）
      const hasSystemMessage = messages.some(
        (msg: any) => msg instanceof SystemMessage
      );
      const messagesWithSystem = hasSystemMessage
        ? messages
        : [systemMessage, ...messages];

      const response = await boundModel.invoke(messagesWithSystem);
      // We return a list, because this will get added to the existing list
      return { messages: [response] };
    };

    const startNode = async (state: typeof MessagesAnnotation.State) => {
      console.log("🚀 Agent started");
      return { messages: state.messages.slice(-1) };
    };

    const finalNode = async (state: typeof MessagesAnnotation.State) => {
      console.log("✅ Agent finished");
      return { messages: state.messages.slice(-1) };
    };

    const graph = new StateGraph(MessagesAnnotation)
      // 添加所有节点
      .addNode("start", startNode) // Agent开始节点
      .addNode("agent", callModel)
      .addNode("tools", toolNode)
      .addNode("final", finalNode)
      // 设置边连接
      .addEdge("__start__", "start") // 从开始到Agent开始节点
      .addEdge("start", "agent") // 从Agent开始到主Agent节点
      // Third parameter is optional and only here to draw a diagram of the graph
      .addConditionalEdges("agent", shouldContinue, {
        tools: "tools",
        final: "final",
      })
      .addEdge("tools", "agent")
      .addEdge("final", "__end__") // 从Agent结束到结束
      .compile({
        checkpointer,
        // interruptBefore: ["tools"],
      });

    return graph;
  }
}
