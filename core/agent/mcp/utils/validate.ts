import path from "path";
import os from 'os';
import fs from "fs/promises";
import { IDE } from "../../..";

// Normalize all paths consistently
function normalizePath(p: string): string {
  return path.normalize(p);
}

function expandHome(filepath: string): string {
  if (filepath.startsWith('~/') || filepath === '~') {
    return path.join(os.homedir(), filepath.slice(1));
  }
  return filepath;
}

// Security utilities
export async function validatePath(requestedPath: string, ide?: IDE): Promise<string> {
  const expandedPath = expandHome(requestedPath);

  let absolute = ''
  const isAbsolute = path.isAbsolute(expandedPath)

  if (isAbsolute) {
    absolute = path.resolve(expandedPath)
  } else {
    const res = await ide?.getWorkspaceDirs();
    if (res?.length === 1) {
      absolute = path.resolve(res[0], expandedPath)
    }
  }

  // Handle symlinks by checking their real path
  try {
    const realPath = await fs.realpath(absolute);
    return realPath;
  } catch (error) {
    // For new files that don't exist yet, verify parent directory
    if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
      const parentDir = path.dirname(absolute);
      try {
        const realParentPath = await fs.realpath(parentDir);
        // const normalizedParent = normalizePath(realParentPath);
        // if (!isPathWithinAllowedDirectories(normalizedParent, allowedDirectories)) {
        //   throw new Error(`Access denied - parent directory outside allowed directories: ${realParentPath} not in ${allowedDirectories.join(', ')}`);
        // }
        return absolute;
      } catch {
        throw new Error(`Parent directory does not exist: ${parentDir}`);
      }
    }
    throw error;
  }
}
