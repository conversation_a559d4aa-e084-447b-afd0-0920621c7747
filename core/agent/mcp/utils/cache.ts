import { ToolResult, Tool<PERSON>all } from '../../stream/types/agent-stream'

export type DiffStatus = 'accepted' | 'rejected' | 'pending';

export interface DiffRecord {
  path?: string;
  content?: string
  status?: DiffStatus,
  added?: number,
  deleted?: number,
}

export interface McpToolCallCacheItem {
  isLoading: boolean;
  isCompleted: boolean;
  isTriggered: boolean;
  isPending: boolean;
  toolCall: ToolCall | undefined;
  toolResult: ToolResult | undefined;
  diff?: DiffRecord | undefined;
}

class McpToolCallCache {
  private cache: Map<string, McpToolCallCacheItem> = new Map();

  public get(key: string) {
    return this.cache.get(key);
  }

  public set(key: string, value: McpToolCallCacheItem) {
    this.cache.set(key, value);
  }

  public delete(key: string) {
    this.cache.delete(key);
  }

  public has(key: string) {
    return this.cache.has(key);
  }
  public clear() {
    this.cache.clear();
  }

  setToolCallDiffInfo(id: string, diffInfo: DiffRecord) {
    const cacheItem = this.cache.get(id);
    debugger
    if (cacheItem) {
      this.cache.set(id, {
        ...cacheItem,
        diff: {
          ...cacheItem.diff,
          ...diffInfo
        }
      });
    } else {
      this.cache.set(id, {
        isLoading: false,
        isCompleted: false,
        isTriggered: false,
        isPending: true,
        toolCall: undefined,
        toolResult: undefined,
        diff: diffInfo
      });
    }
  }

  // 调用前设置值
  beforeToolCall(id: string, toolCall: ToolCall) {
    const cacheItem = this.cache.get(id);

    if (cacheItem) {
      this.cache.set(id, {
        ...cacheItem,
        isLoading: false,
        isCompleted: false,
        isTriggered: false,
        isPending: true,
      });
      this.setToolCall(id, toolCall);
    } else {
      this.cache.set(id, {
        isLoading: false,
        isCompleted: false,
        isTriggered: false,
        isPending: true,
        toolCall: toolCall,
        toolResult: undefined,
        diff: undefined,
      });
    }
  }

  onToolCall(id: string) {
    const cacheItem = this.cache.get(id);

    if (cacheItem) {
      this.cache.set(id, {
        ...cacheItem,
        isLoading: false,
        isCompleted: true,
        isTriggered: true,
        isPending: false,
      });
    }
  }

  // 调用后设置值
  afterToolCall(id: string, toolResult: ToolResult) {
    const cacheItem = this.cache.get(id)
    debugger
    if (cacheItem) {
      this.cache.set(id, {
        ...cacheItem,
        isLoading: false,
        isCompleted: true,
        isTriggered: true,
        isPending: false,
      });
      this.setToolResult(id, toolResult);
    }
  }

  setToolCall(id: string, toolCall: ToolCall) {
    const cacheItem = this.cache.get(id);

    if (cacheItem) {
      this.cache.set(id, {
        ...cacheItem,
        toolCall: toolCall,
      });
    }
  }

  setToolResult(id: string, toolResult: ToolResult) {
    const cacheItem = this.cache.get(id);

    if (cacheItem) {
      this.cache.set(id, {
        ...cacheItem,
        toolResult: toolResult,
      });
    }
  }
  getValues() {
    return Array.from(this.cache.values());
  }
}

export const mcpToolsCallCache = new McpToolCallCache();