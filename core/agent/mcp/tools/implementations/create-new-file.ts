import { tool } from "@langchain/core/tools";
import { ZodDefinition, CreateNewFileArgsSchema } from '../definitions/create-new-file'
import { validatePath } from '../../utils/validate'

export const createNewFile = tool(
  async (input: { path: string; contents: string }, config: any) => {
    try {
      const parsed = CreateNewFileArgsSchema.safeParse(input);
      if (!parsed.success) {
        throw new Error(`Invalid arguments for createNewFile: ${parsed.error}`);
      }

      const { ide } = config.configurable || {}
      const validPath = await validatePath(parsed.data.path, ide);

      await ide.writeFile(validPath, input.contents);
      await ide.openFile(validPath);

      return {
        content: [{ type: "text", text: `Successfully wrote to ${parsed.data.path}` }],
      };

    } catch (error) {
      return {
        content: [{ type: "text", text: error }],
      };
    }

  },
  ZodDefinition
);
