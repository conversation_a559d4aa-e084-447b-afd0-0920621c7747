import { tool } from "@langchain/core/tools";
import { getUriDescription } from "../../../../utils/uri";
import { ZodDefinition } from '../definitions/read-current-open-file'

export const readCurrentlyOpenFile = tool(
  async (_input: {}, config: any) => {
    const { ide } = config.configurable || {}
    const result = await ide.getCurrentFile();
    if (!result) {
      return {};
    }
    const { relativePathOrBasename } = getUriDescription(
      result.path,
      await ide.getWorkspaceDirs(),
    );

    return {
      content: [{
        type: "text",
        text: `\\u00a0\u00a0\u00a0${relativePathOrBasename}\n${result.contents}\n\\u00a0\u00a0\u00a0`,
      }]
    }
  },
  ZodDefinition
); 