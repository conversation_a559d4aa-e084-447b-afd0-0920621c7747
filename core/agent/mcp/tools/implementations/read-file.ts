import { tool } from "@langchain/core/tools";
import { ZodDefinition, ReadFileArgsSchema } from "../definitions/read-file";
import fs from "fs/promises";
import { validatePath } from '../../utils/validate'

export const readFile = tool(
  async (input: { path: string }, config: any) => {
    const parsed = ReadFileArgsSchema.safeParse(input);

    if (!parsed.success) {
      throw new Error(`Invalid arguments for readFile: ${parsed.error}`);
    }

    const { ide } = config.configurable || {}
    const validPath = await validatePath(parsed.data.path, ide);
    const content = await ide.readFile(validPath);

    return {
      content: [{ type: "text", text: content }],
    };
  },
  ZodDefinition
);