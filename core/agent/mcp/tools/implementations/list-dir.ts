import { tool } from "@langchain/core/tools";
import { promises as fs } from "fs";
import { ZodDefinition, ListDirArgsSchema } from "../definitions/list-dir";
import { validatePath } from '../../utils/validate'

/**
 * 列出目录内容的工具
 * 结果中以 / 结尾的是文件夹，否则是文件
 */
export const listDir = tool(
  async (input: { path: string }, config) => {

    const parsed = ListDirArgsSchema.safeParse(input);
    if (!parsed.success) {
      throw new Error(`Invalid arguments for listDir: ${parsed.error}`);
    }
    const { ide } = config.configurable || {}

    const validPath = await validatePath(parsed.data.path, ide);
    const entries = await fs.readdir(validPath, { withFileTypes: true });

    const formatted = entries
      .map((entry) => `${entry.isDirectory() ? "[DIR]" : "[FILE]"} ${entry.name}`)
      .join("\n");

    return {
      content: [{ type: "text", text: formatted }],
    };
  },
  ZodDefinition
);
