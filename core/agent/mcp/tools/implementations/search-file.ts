import { tool } from "@langchain/core/tools";
import { promises as fs } from "fs";
import { ZodDefinition, SearchFilesArgsSchema } from "../definitions/search-files";
import path from "path";
import { minimatch } from 'minimatch';
import { validatePath } from '../../utils/validate'

export const searchFiles = tool(
  async (input: { path: string, pattern: string, excludePatterns: string[] }, config) => {

    const parsed = SearchFilesArgsSchema.safeParse(input);
    if (!parsed.success) {
      throw new Error(`Invalid arguments for searchFiles: ${parsed.error}`);
    }
    const { ide } = config.configurable || {}

    const validPath = await validatePath(parsed.data.path, ide);
    const results = await _searchFiles(validPath, input.pattern, input.excludePatterns);

    return {
      content: [{ type: "text", text: results.length > 0 ? results.join("\n") : "No matches found" }],
    };
  },
  ZodDefinition
);

async function _searchFiles(
  rootPath: string,
  pattern: string,
  excludePatterns: string[] = []
): Promise<string[]> {
  const results: string[] = [];

  async function search(currentPath: string) {
    const entries = await fs.readdir(currentPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(currentPath, entry.name);

      try {
        // Validate each path before processing
        // await validatePath(fullPath);

        // Check if path matches any exclude pattern
        const relativePath = path.relative(rootPath, fullPath);
        const shouldExclude = excludePatterns.some(pattern => {
          const globPattern = pattern.includes('*') ? pattern : `**/${pattern}/**`;
          return minimatch(relativePath, globPattern, { dot: true });
        });

        if (shouldExclude) {
          continue;
        }

        if (entry.name.toLowerCase().includes(pattern.toLowerCase())) {
          results.push(fullPath);
        }

        if (entry.isDirectory()) {
          await search(fullPath);
        }
      } catch (error) {
        // Skip invalid paths during search
        continue;
      }
    }
  }

  await search(rootPath);
  return results;
}