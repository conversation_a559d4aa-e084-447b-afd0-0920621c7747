import { tool } from "@langchain/core/tools";
import { promises as fs } from "fs";
import { ZodDefinition, MoveFileArgsSchema } from "../definitions/move-file";
import { validatePath } from '../../utils/validate'

/**
 * 列出目录内容的工具
 * 结果中以 / 结尾的是文件夹，否则是文件
 */
export const moveFile = tool(
  async (input: { source: string, destination: string }, config) => {
    const parsed = MoveFileArgsSchema.safeParse(input);
    if (!parsed.success) {
      throw new Error(`Invalid arguments for moveFile: ${parsed.error}`);
    }
    const { ide } = config.configurable || {}

    const validSourcePath = await validatePath(parsed.data.source, ide);
    const validDestPath = await validatePath(parsed.data.destination, ide);
    await fs.rename(validSourcePath, validDestPath);

    return {
      content: [{ type: "text", text: `Successfully moved ${parsed.data.source} to ${parsed.data.destination}` }],
    };
  },
  ZodDefinition
);
