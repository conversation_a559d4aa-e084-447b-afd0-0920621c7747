import { tool } from "@langchain/core/tools";
import { ZodDefinition, EditFileArgsSchema } from '../definitions/edit-file'
import { validatePath } from '../../utils/validate'
import { applyFileEdits, getGitDiffStats } from '../../../../utils/diff'
import { IGetErrorsResponse } from '../../../../'
import { renderFileErrors } from '../../utils/render-file-error'

export const editFile = tool(
  async (input: { path: string; edits: { oldText: string; newText: string }[] }, config: any) => {

    const parsed = EditFileArgsSchema.safeParse(input);

    if (!parsed.success) {
      throw new Error(`Invalid arguments for editFile: ${parsed.error}`);
    }
    const { edits } = input;
    const { ide } = config.configurable || {}
    const validPath = await validatePath(parsed.data.path, ide);

    try {
      const { ide } = config.configurable || {}
      const originalContent = await ide.readFile(validPath);
      const { content, diff } = await applyFileEdits(originalContent, validPath, edits);
      const { added, deleted } = getGitDiffStats(originalContent, diff)

      await ide.fileShowDiff(validPath, content);

      const diagnostics: IGetErrorsResponse[] = await ide.getErrors([validPath]);
      const fileErrors = renderFileErrors(diagnostics);

      if (fileErrors.length) {
        return {
          content: [{ type: "text", text: fileErrors }]
        }
      }
      return {
        content: [{ type: "text", text: content, extraData: { added, deleted } }]
      }
    } catch (error) {
      return {
        content: [{ type: "text", text: error }]
      }
    }
  },
  ZodDefinition
);