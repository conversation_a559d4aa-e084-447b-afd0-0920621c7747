import { tool } from "@langchain/core/tools";
import { promises as fs } from "fs";
import { ZodDefinition, CreateDirectoryArgsSchema } from "../definitions/create-directory";
import { validatePath } from '../../utils/validate'

export const createDirectory = tool(
  async (input: { path: string }, config) => {
    try {
      const parsed = CreateDirectoryArgsSchema.safeParse(input);
      if (!parsed.success) {
        throw new Error(`Invalid arguments for createDirectory: ${parsed.error}`);
      }
      const { ide } = config.configurable || {}
      const validPath = await validatePath(parsed.data.path, ide);
      // 递归创建目录结构
      await fs.mkdir(validPath, { recursive: true });
      return {
        content: [{ type: "text", text: `Successfully created directory ${parsed.data.path}` }],
      };
    } catch (error: any) {
      console.error("Error creating directory:", error.message);
      throw new Error(`Failed to create directory: ${error.message}`);
    }
  },
  ZodDefinition
);