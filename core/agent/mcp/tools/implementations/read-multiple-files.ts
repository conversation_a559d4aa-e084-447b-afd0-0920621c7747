import { tool } from "@langchain/core/tools";
import { ZodDefinition, ReadMultipleFilesArgsSchema } from "../definitions/read-multiple-files";
import fs from "fs/promises";
import { validatePath } from '../../utils/validate'

export const readMultipleFiles = tool(
  async (input: { paths: string[] }, config) => {
    const parsed = ReadMultipleFilesArgsSchema.safeParse(input);

    if (!parsed.success) {
      throw new Error(`Invalid arguments for readMultipleFiles: ${parsed.error}`);
    }
    const { ide } = config.configurable || {}

    const results = await Promise.all(
      parsed.data.paths.map(async (filePath: string) => {
        try {
          const validPath = await validatePath(filePath, ide);
          const content = await fs.readFile(validPath, "utf-8");
          return `${filePath}:\n${content}\n`;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          return `${filePath}: Error - ${errorMessage}`;
        }
      }),
    );

    return {
      content: [{ type: "text", text: results.join("\n---\n") }],
    };
  },
  ZodDefinition
);