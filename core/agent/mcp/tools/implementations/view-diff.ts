import { tool } from "@langchain/core/tools";
import { ZodDefinition } from "../definitions/view-diff";

export const viewDiff = tool(
  async (_input: {}, config: any) => {
    const { ide } = config.configurable || {}
    const diff = await ide.getDiff(true);

    return {
      content: [
        {
          type: "text",
          text: diff.join("\n"),
        },
      ]
    }
  },
  ZodDefinition
);