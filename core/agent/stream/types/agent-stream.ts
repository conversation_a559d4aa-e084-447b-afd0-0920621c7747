/**
 * Agent 流式输出数据结构定义
 * 基于 agent-stream-data-structure-README.md 文档实现
 */


// 消息类型枚举
export enum MessageType {
  HUMAN_MESSAGE = 'human_message',
  AGENT_THINKING = 'agent_thinking', 
  TOOL_CALL_REQUEST = 'tool_call_request',
  TOOL_CALL_RESULT = 'tool_call_result',
  AGENT_CHUNK_RESPONSE = 'agent_chunk_response',
  MESSAGE_END = 'message_end'
}

// 工具调用状态枚举
export enum ToolCallStatus {
  SUCCESS = 'success',
  ERROR = 'error',
  TIMEOUT = 'timeout'
}

// 基础消息结构接口
export interface BaseMessage {
  metaData: {
    conversionId: string;       // 对话ID
    messageId: string;           // 消息ID
  },
  timestamp: string;           // ISO 8601 格式时间戳
  messageType: MessageType;    // 消息类型枚举
  data: MessageData;           // 消息具体内容
}

// 消息数据联合类型
export type MessageData = 
  | HumanMessageData
  | AgentThinkingData
  | ToolCallRequestData
  | ToolCallResultData
  | AgentChunkResponseData
  | MessageEndData;

// 内容结构接口
export interface ContentStructure {
  text: string;
}

// 1. 用户消息数据结构
export interface HumanMessageData {
  content: ContentStructure;
}

// 2. Agent 思考过程数据结构
export interface AgentThinkingData {
  content: ContentStructure;
}

// 3. 工具调用请求数据结构
export interface ToolCallRequestData {
  toolCall: ToolCallStructure;
}

// 4. 工具调用结果数据结构
export interface ToolCallResultData {
  toolResult: ToolResult;
}

export interface ToolResult {
  id: string;          // 对应的工具调用ID
  name: string;                // 工具方法名
  status: ToolCallStatus;      // 执行状态
  result?: any; // 工具执行结果（可选）
  error?: ToolError;           // 错误信息（仅当 status 为 error 时）
}

export interface ToolError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// 工具调用接口
export interface ToolCallStructure {
  index?: number;
  id?: string;           // 工具调用唯一标识符
  type?: string;     // 工具调用类型
  name?: string;         // 工具方法名
  args?: string | any;    // 工具调用参数
}

// 5. Agent 响应数据结构
export interface AgentChunkResponseData {
  content?: ContentStructure; // 二者至少有一
  tool_call?: ToolCallStructure;
}

// 6. 消息结束标记数据结构
export interface MessageEndData {
  messageComplete: boolean;    // 消息是否完整
}

// 具体消息类型接口
export interface HumanMessage extends BaseMessage {
  messageType: MessageType.HUMAN_MESSAGE;
  data: HumanMessageData;
}

export interface AgentThinkingMessage extends BaseMessage {
  messageType: MessageType.AGENT_THINKING;
  data: AgentThinkingData;
}

export interface ToolCallRequestMessage extends BaseMessage {
  messageType: MessageType.TOOL_CALL_REQUEST;
  data: ToolCallRequestData;
}

export interface ToolCallResultMessage extends BaseMessage {
  messageType: MessageType.TOOL_CALL_RESULT;
  data: ToolCallResultData;
}

export interface AgentChunkResponseMessage extends BaseMessage {
  messageType: MessageType.AGENT_CHUNK_RESPONSE;
  data: AgentChunkResponseData;
}

export interface MessageEndMessage extends BaseMessage {
  messageType: MessageType.MESSAGE_END;
  data: MessageEndData;
}

// 消息联合类型
export type StreamMessage = 
  | HumanMessage
  | AgentThinkingMessage
  | ToolCallRequestMessage
  | ToolCallResultMessage
  | AgentChunkResponseMessage
  | MessageEndMessage;

// 消息创建工厂函数
export class MessageFactory {
  /**
   * 创建用户消息
   */
  static createHumanMessage(
    metaData: {
      conversionId: string;
      messageId: string;
    },
    text: string,
    timestamp?: string
  ): HumanMessage {
    return {
      metaData,
      timestamp: timestamp || new Date().toISOString(),
      messageType: MessageType.HUMAN_MESSAGE,
      data: {
        content: { text }
      }
    };
  }

  /**
   * 创建 Agent 思考消息
   */
  static createAgentThinkingMessage(
    metaData: {
      conversionId: string;
      messageId: string;
    },
    text: string,
    timestamp?: string
  ): AgentThinkingMessage {
    return {
      metaData,
      timestamp: timestamp || new Date().toISOString(),
      messageType: MessageType.AGENT_THINKING,
      data: {
        content: { text }
      }
    };
  }

  /**
   * 创建工具调用请求消息
   */
  static createToolCallRequestMessage(
    metaData: {
      conversionId: string;
      messageId: string;
    },
    toolCall: ToolCallStructure,
    timestamp?: string
  ): ToolCallRequestMessage {
    return {
      metaData,
      timestamp: timestamp || new Date().toISOString(),
      messageType: MessageType.TOOL_CALL_REQUEST,
      data: { toolCall }
    };
  }

  /**
   * 创建工具调用结果消息
   */
  static createToolCallResultMessage(
    metaData: {
      conversionId: string;
      messageId: string;
    },
    toolResult: ToolResult,
    timestamp?: string
  ): ToolCallResultMessage {
    return {
      metaData,
      timestamp: timestamp || new Date().toISOString(),
      messageType: MessageType.TOOL_CALL_RESULT,
      data: { toolResult }
    };
  }

  /**
   * 创建 Agent 响应消息
   * @param metaData 元数据
   * @param text 响应文本内容
   * @param toolCall 可选的工具调用信息
   * @param timestamp 可选的时间戳，不传则使用当前时间
   */
  static createAgentChunkResponseMessage(
    metaData: {
      conversionId: string;
      messageId: string;
    },
    text?: string,
    toolCall?: ToolCallStructure,
    timestamp?: string
  ): AgentChunkResponseMessage {
    return {
      metaData,
      timestamp: timestamp || new Date().toISOString(),
      messageType: MessageType.AGENT_CHUNK_RESPONSE,
      data: {
        content: text? { text } : undefined,
        tool_call: toolCall? toolCall : undefined
      }
    };
  }

  /**
   * 创建消息结束标记
   */
  static createMessageEndMessage(
    metaData: {
      conversionId: string;
      messageId: string;
    },
    messageComplete: boolean = true,
    timestamp?: string
  ): MessageEndMessage {
    return {
      metaData,
      timestamp: timestamp || new Date().toISOString(),
      messageType: MessageType.MESSAGE_END,
      data: {
        messageComplete
      }
    };
  }
}

// 类型守卫函数
export class MessageTypeGuards {
  static isHumanMessage(message: StreamMessage): message is HumanMessage {
    return message.messageType === MessageType.HUMAN_MESSAGE;
  }

  static isAgentThinkingMessage(message: StreamMessage): message is AgentThinkingMessage {
    return message.messageType === MessageType.AGENT_THINKING;
  }

  static isToolCallRequestMessage(message: StreamMessage): message is ToolCallRequestMessage {
    return message.messageType === MessageType.TOOL_CALL_REQUEST;
  }

  static isToolCallResultMessage(message: StreamMessage): message is ToolCallResultMessage {
    return message.messageType === MessageType.TOOL_CALL_RESULT;
  }

  static isAgentChunkResponseMessage(message: StreamMessage): message is AgentChunkResponseMessage {
    return message.messageType === MessageType.AGENT_CHUNK_RESPONSE;
  }

  static isMessageEndMessage(message: StreamMessage): message is MessageEndMessage {
    return message.messageType === MessageType.MESSAGE_END;
  }
}
