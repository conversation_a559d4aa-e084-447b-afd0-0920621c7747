/**
 * Agent 流式输出数据结构库
 * 主入口文件
 */

// 导出核心类型定义
export type {
  
  // 基础接口
  BaseMessage,
  MessageData,
  ContentStructure,
  
  // 数据结构接口
  HumanMessageData,
  AgentThinkingData,
  ToolCallRequestData,
  ToolCallResultData,
  AgentChunkResponseData,
  MessageEndData,
  
  // 工具相关接口
  ToolResult,
  ToolError,
  
  // 具体消息类型接口
  HumanMessage,
  AgentThinkingMessage,
  ToolCallRequestMessage,
  ToolCallResultMessage,
  AgentChunkResponseMessage,
  MessageEndMessage,
  
  // 联合类型
  StreamMessage,
  
} from './types/agent-stream';

// 导出工厂类和类型守卫
export {
  // 枚举类型
  MessageType,
  ToolCallStatus,
  MessageFactory,
  MessageTypeGuards
} from './types/agent-stream';
