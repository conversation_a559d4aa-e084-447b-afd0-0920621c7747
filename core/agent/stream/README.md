# Agent 流式输出数据结构设计文档

## 概述

本文档描述了 Agent 系统流式输出的数据结构设计，用于实时传输对话过程中的各种消息类型，包括用户消息、Agent 响应、工具调用等。

## 核心设计原则

1. **流式传输**：支持实时数据流传输，提供良好的用户体验
2. **类型明确**：通过 `messageType` 字段明确区分不同类型的消息
3. **可扩展性**：数据结构设计支持未来功能扩展
4. **一致性**：所有消息遵循统一的基础结构

## 基础数据结构

### 消息基础结构

```json
{
  "metaData": {
    "conversionId": "string",       // 对话ID
    "messageId": "string"           // 消息ID
  },
  "timestamp": "string",           // ISO 8601 格式时间戳
  "messageType": "string",         // 消息类型枚举
  "data": {                        // 消息具体内容
    // 根据 messageType 变化的数据结构
  }
}
```

## 消息类型定义

### 1. human_message - 用户消息
用户输入的消息内容。

```json
{
  "messageType": "human_message",
  "data": {
    "content": {
      "text": "string"           // 用户输入的文本内容
    }
  }
}
```

### 2. agent_thinking - Agent 思考过程
Agent 的内部思考过程，可选择性展示给用户。

```json
{
  "messageType": "agent_thinking", 
  "data": {
    "content": {
      "text": "string"           // Agent 的思考内容
    }
  }
}
```

### 3. tool_call_request - 工具调用请求
Agent 发起工具调用的请求。

```json
{
  "messageType": "tool_call_request",
  "data": {
    "toolCall": {
      "index": number,
      "id": "string",             // 工具调用唯一标识符
      "type": "string",           // 工具调用类型
      "name": "string",         // 工具方法名
      "args": "string"    // 工具调用参数
    }
  }
}
```

### 4. tool_call_result - 工具调用结果
工具执行完成后返回的结果。

```json
{
  "messageType": "tool_call_result",
  "data": {
    "toolResult": {
      "id": "string",     // 对应的工具调用ID
      "name": "string",           // 工具方法名
      "status": "string",         // 执行状态：success/error/timeout
      "result": {                 // 工具执行结果
        // 根据具体工具变化的结果结构
      },
      "error": {                  // 错误信息（仅当 status 为 error 时）
        "code": "string",
        "message": "string",
        "details": {}
      }
    }
  }
}
```

### 5. agent_chunk_response - Agent 响应
Agent 对用户的文本回复。

```json
{
  "messageType": "agent_chunk_response",
  "data": {
    "content": {
      "text": "string"           // Agent 的回复文本
    },
    "toolCall": {
      "index": number,
      "id": "string",             // 工具调用唯一标识符
      "type": "string",           // 工具调用类型
      "name": "string",         // 工具方法名
      "args": "string"    // 工具调用参数
    }
  }
}
```

### 6. message_end - 消息结束标记
标记一个完整消息的结束。

```json
{
  "messageType": "message_end",
  "data": {
    "messageComplete": true,      // 消息是否完整
    "totalChunks": "number"       // 该消息的总块数
  }
}
```

## 消息流程示例

### 典型的工具调用流程

1. **用户消息** (`human_message`)
2. **Agent 思考** (`agent_thinking`) - 可选
3. **工具调用请求** (`tool_call_request`)
4. **工具调用结果** (`tool_call_result`)
5. **Agent 响应** (`agent_chunk_response`) - 可能分多个块
6. **消息结束** (`message_end`)

### 简单问答流程

1. **用户消息** (`human_message`)
2. **Agent 响应** (`agent_chunk_response`) - 可能分多个块
3. **消息结束** (`message_end`)

## 改进建议总结

### 已实现的改进

1. **结构化消息类型**：明确区分不同类型的消息，提高可读性
2. **统一的基础结构**：所有消息遵循相同的基础格式
3. **工具调用状态管理**：增加了工具调用的状态和错误处理
4. **消息完整性标记**：通过 `message_end` 确保消息完整性
5. **思考过程透明化**：增加 `agent_thinking` 类型展示 Agent 思考过程

### 未来扩展方向

1. **多媒体支持**：扩展 content 结构支持图片、文件等
2. **消息优先级**：增加优先级字段支持紧急消息处理
3. **消息关联**：增加 `replyTo` 字段支持消息回复关系
4. **批量操作**：支持批量工具调用和结果处理
5. **流控制**：增加流控制机制，支持暂停/恢复
6. **消息压缩**：对大型消息内容进行压缩传输

## 使用注意事项

1. **时间戳一致性**：确保所有相关消息的时间戳逻辑一致
2. **错误处理**：工具调用失败时必须提供详细的错误信息
3. **消息顺序**：通过 chunkIndex 确保消息块的正确顺序
4. **资源清理**：长时间会话需要考虑消息历史的清理机制
5. **安全性**：敏感信息（如API密钥）不应出现在日志中

## 版本兼容性

当前版本：v1.0
- 向后兼容：新增字段不影响现有解析逻辑
- 向前兼容：保留必要的基础字段确保旧版本可用
