{
  // 使用 IntelliSense 了解相关属性。
  // 悬停以查看现有属性的描述。
  // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Run Extension",
      "type": "extensionHost",
      "request": "launch",
      "args": [
        "--extensionDevelopmentPath=${workspaceFolder}/vscode-extension", 
        "${workspaceFolder}/manual-testing-sandbox",
        "--disable-extensions",
        "--enable-proposed-api",  // 启用提案 API
        "cucloud.cucloud-ai-code-assistant"  // 你的扩展 ID（package.json 中的 publisher + name）
      ],
      "outFiles": ["${workspaceFolder}/vscode-extension/dist/**/*.js"]
      // "preLaunchTask": "npm: watch:esbuild"
    },
  ]
}
