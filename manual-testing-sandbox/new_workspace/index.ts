function testMethod1(): void {
    console.log('Test Method 1 executed');
}

function testMethod2(): void {
    console.log('Test Method 2 executed');
}

// Call the test methods
testMethod1();
testMethod2();// This is the main TypeScript file

function testFunction1(): void {
    console.log('Test function 1');
}

function testFunction2(): void {
    console.log('Test function 2');
}

function testFunction3(): void {
    console.log('Test function 3');
}

function testFunction4(): void {
    console.log('Test function 4');
}