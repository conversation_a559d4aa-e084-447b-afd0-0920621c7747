import * as fs from 'node:fs';
import * as os from 'node:os';
import * as path from 'node:path';
import * as vscode from 'vscode';
import { getPlatform } from '../utils/util';
import { uriFromFilePath } from '../utils/vscode';
import { getFileNameByPath } from 'core/utils/file'
import { cleanStoreDiff } from '../utils/diff';
import { ensureAbsolutePath } from '../utils/path'

interface DiffInfo {
  snapshotFilepath: string; // .diffs 下的快照文件（旧内容）
  projectFilepath: string; // 项目文件（新内容）
  editor?: vscode.TextEditor;
  step_index: number;
  range: vscode.Range;
}

async function readFile(path: string): Promise<string> {
  return await vscode.workspace.fs
    .readFile(uriFromFilePath(path))
    .then((bytes) => new TextDecoder().decode(bytes));
}

async function writeFile(uri: vscode.Uri, contents: string) {
  await vscode.workspace.fs.writeFile(uri, new TextEncoder().encode(contents));
}

// THIS IS LOCAL
export const DIFF_DIRECTORY = path
  .join(os.homedir(), '.cuc-code-assistant', '.diffs')
  .replace(/^C:/, 'c:');


const setRenderSideBySide = async (bool: boolean) => {
  await vscode.workspace.getConfiguration('diffEditor').update(
    'renderSideBySide',
    bool,
    vscode.ConfigurationTarget.Global
  )
}

export class DiffManager {
  private diffs: Map<string, DiffInfo> = new Map();
  private originalRenderSideBySide: boolean | undefined = undefined;

  diffAtProjectFilepath(projectFilepath: string): DiffInfo | undefined {
    return this.diffs.get(projectFilepath);
  }

  private async setupDirectory() {
    if (!fs.existsSync(DIFF_DIRECTORY)) {
      fs.mkdirSync(DIFF_DIRECTORY, {
        recursive: true
      });
    }
  }

  constructor(private readonly extensionContext: vscode.ExtensionContext) {
    this.setupDirectory();
    vscode.workspace.onDidCloseTextDocument((document) => {
      const projectFilepath = document.uri.fsPath;
      const diffInfo = this.diffs.get(projectFilepath);
      if (diffInfo) {
        this.cleanUpDiff(diffInfo, false);
      }
    });
  }

  private escapeFilepath(filepath: string): string {
    return filepath.replace(/\//g, '_f_').replace(/\\/g, '_b_').replace(/:/g, '_c_');
  }

  public getSnapshotFilepath(projectFilepath: string): string {
    return path.join(DIFF_DIRECTORY, this.escapeFilepath(projectFilepath));
  }

  private async openDiffEditor(
    snapshotFilepath: string,
    projectFilepath: string,
  ): Promise<vscode.TextEditor | undefined> {
    try {
      await vscode.workspace.fs.stat(uriFromFilePath(snapshotFilepath));
      await vscode.workspace.fs.stat(uriFromFilePath(projectFilepath));
    } catch (e) {
      console.log("Snapshot or project file doesn't exist, not opening diff editor", e);
      return undefined;
    }
    if (path.basename(projectFilepath).match(/^\d$/)) {
      return undefined;
    }
    const leftUri = uriFromFilePath(snapshotFilepath); // 旧内容
    const rightUri = uriFromFilePath(projectFilepath); // 新内容
    const _title = getFileNameByPath(projectFilepath) || 'diff';

    // await setRenderSideBySide(false)
    vscode.commands.executeCommand('vscode.diff', leftUri, rightUri, _title);
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      return;
    }
    vscode.workspace
      .getConfiguration('diffEditor', editor.document.uri)
      .update('codeLens', true, vscode.ConfigurationTarget.Global);
    return editor;
  }

  private _findFirstDifferentLine(contentA: string, contentB: string): number {
    const linesA = contentA.split('\n');
    const linesB = contentB.split('\n');
    for (let i = 0; i < linesA.length && i < linesB.length; i++) {
      if (linesA[i] !== linesB[i]) {
        return i;
      }
    }
    return 0;
  }

  async writeDiff(filepath: string, newContent: string): Promise<string> {
    let projectFilepath = ensureAbsolutePath(filepath)

    await this.setupDirectory();
    const snapshotFilepath = this.getSnapshotFilepath(projectFilepath);
    const snapshotUri = uriFromFilePath(snapshotFilepath);
    // 只在没有快照时保存旧内容
    try {
      await vscode.workspace.fs.stat(snapshotUri);
    } catch {
      const oldContent = await readFile(projectFilepath);
      await writeFile(snapshotUri, oldContent);
    }
    // 每次都用 newContent 覆盖项目文件
    await writeFile(uriFromFilePath(projectFilepath), newContent);
    // 打开 diff 编辑器
    if (!this.diffs.has(projectFilepath)) {
      const oldContent = await readFile(snapshotFilepath);
      const line = this._findFirstDifferentLine(oldContent, newContent);
      const diffInfo: DiffInfo = {
        snapshotFilepath,
        projectFilepath,
        step_index: 0,
        range: new vscode.Range(line, 0, line + 1, 0)
      };
      this.diffs.set(projectFilepath, diffInfo);
    }
    const diffInfo = this.diffs.get(projectFilepath);
    if (diffInfo && !diffInfo?.editor) {
      diffInfo.editor = await this.openDiffEditor(snapshotFilepath, projectFilepath);
      this.diffs.set(projectFilepath, diffInfo);
    }
    if (getPlatform() === 'windows') {
      vscode.commands.executeCommand('workbench.action.files.revert', uriFromFilePath(projectFilepath));
    }
    return projectFilepath;
  }

  cleanUpDiff(diffInfo: DiffInfo, hideEditor = true) {
    if (hideEditor && diffInfo?.editor) {
      try {
        vscode.window.showTextDocument(diffInfo.editor.document);
        vscode.commands.executeCommand('workbench.action.closeActiveEditor');
      } catch (err) {
        console.log('cleanUpDiff-error', err);
      }
    }
    this.diffs.delete(diffInfo.projectFilepath);
    vscode.workspace.fs.delete(uriFromFilePath(diffInfo.snapshotFilepath));
    // setRenderSideBySide(true)
    cleanStoreDiff()
  }

  private inferProjectFilepath() {
    const activeEditorPath = vscode.window.activeTextEditor?.document.uri.fsPath;
    if (activeEditorPath && this.diffs.has(activeEditorPath)) {
      return activeEditorPath;
    }
    const visibleEditors = vscode.window.visibleTextEditors.map(
      (editor) => editor.document.uri.fsPath
    );
    for (const editorPath of visibleEditors) {
      if (this.diffs.has(editorPath)) {
        return editorPath;
      }
    }
    if (this.diffs.size === 1) {
      return Array.from(this.diffs.keys())[0];
    }
    return activeEditorPath;
  }

  async acceptDiff(projectFilepath?: string) {
    if (projectFilepath) {
      projectFilepath = ensureAbsolutePath(projectFilepath)
    }
    if (!projectFilepath) {
      projectFilepath = this.inferProjectFilepath();
    }
    console.log('projectFilepath', projectFilepath);
    if (!projectFilepath) {
      console.log('No projectFilepath provided to accept the diff');
      return;
    }
    const diffInfo = this.diffs.get(projectFilepath);
    console.log('diffInfo', diffInfo);

    const targetUri = vscode.Uri.file(projectFilepath);

    await this.closeEditorByUri(targetUri);
    // 关闭 diff 预览编辑器
    vscode.commands.executeCommand('workbench.action.closeActiveEditor')

    // 打开项目文件的普通编辑器
    await vscode.window.showTextDocument(vscode.Uri.file(projectFilepath), { preview: false });

    if (diffInfo) {
      // 清理缓存和快照
      this.cleanUpDiff(diffInfo, false);
      await recordAcceptReject(true, diffInfo);
    }
  }

  async rejectDiff(projectFilepath?: string) {
    if (projectFilepath) {
      projectFilepath = ensureAbsolutePath(projectFilepath)
    }
    if (!projectFilepath) {
      projectFilepath = this.inferProjectFilepath();
    }
    if (!projectFilepath) {
      console.log('No projectFilepath provided to reject the diff, diffs.size was', this.diffs.size);
      return;
    }
    const diffInfo = this.diffs.get(projectFilepath);

    const filepath = diffInfo ? diffInfo.projectFilepath : projectFilepath
    const snapshotFilepath = diffInfo ? diffInfo.snapshotFilepath : this.getSnapshotFilepath(projectFilepath)
    const snapshotContent = await readFile(snapshotFilepath);


    const targetUri = vscode.Uri.file(projectFilepath);
    await this.closeEditorByUri(targetUri);

    // // 2. 恢复文件内容 用快照内容覆盖项目文件
    await writeFile(uriFromFilePath(filepath), snapshotContent);
    await vscode.commands.executeCommand('workbench.action.closeActiveEditor');

    // 3. 打开项目文件的普通编辑器
    await vscode.window.showTextDocument(vscode.Uri.file(projectFilepath), { preview: false });

    // 4. 清理缓存
    if (diffInfo) {
      this.cleanUpDiff(diffInfo, false);
      await recordAcceptReject(false, diffInfo);
    }
  }

  async closeEditorByUri(targetUri: vscode.Uri) {
    // 找到所有打开的文档
    const allDocuments = vscode.workspace.textDocuments;
    // 筛选出目标文档
    const targetDoc = allDocuments.find(doc => doc.uri.fsPath === targetUri.fsPath);

    if (targetDoc) {
      // 关闭文档对应的编辑器
      await vscode.commands.executeCommand('workbench.action.closeEditorsInGroup', {
        uris: [targetDoc.uri], // 指定要关闭的文档 URI
        group: -1 // -1 表示当前活动组，可指定具体组
      });
    }
  }
}

async function recordAcceptReject(accepted: boolean, diffInfo: DiffInfo) {
  let suggestions = [];
  suggestions.push({
    accepted,
    timestamp: Date.now(),
    suggestion: diffInfo.projectFilepath
  });
}
