import { IdeInfo } from 'core';
import Logger from 'core/logger';
import { FromCoreProtocol, FromWebviewProtocol, Message, ToCoreProtocol } from 'core/protocol';
import { WEBVIEW_TO_CORE_PASS_THROUGH, CORE_TO_WEBVIEW_PASS_THROUGH } from 'core/protocol/pass-through'
import { ToIdeFromCoreProtocol, ToIdeFromWebviewOrCoreProtocol } from 'core/protocol/ide';
import { InProcessMessenger } from 'core/protocol/messenger';
import { ToWebviewFromCoreProtocol } from 'core/protocol/webview';
import * as vscode from 'vscode';
import { CodeAssistantAuth } from '../auth/code-assistant-auth';
import configuration from './configuration';
import { VsCodeIde } from '../ide-protocol';
import { logout } from '../utils/logout'
import store from '../store';
import { setAllowClearHistory, setAllowOpenHistory } from '../utils/context';
import { downloadFile } from '../utils/download';
import { acceptCodeToEditor, cancelEditorSelection, getUpdatedContent } from '../utils/editor';
import { openUrl } from '../utils/url';
import { infoPopup } from '../utils/vscode';
import VsCodeWebviewProtocol from '../webview/webview-protocol';
import { setupStatusBar, stopStatusBarLoading } from '../autocomplete/status-bar'
import { GitUtil } from '../utils/git-util';
import { DIFF_FLAG } from '../constants'
import { setStoreDiff } from '../utils/diff'

type FromWebviewProtocolCommand = keyof FromWebviewProtocol;
type WebviewMessageHandler<T extends FromWebviewProtocolCommand> = (
  message: Message<FromWebviewProtocol[T][0]>
) => Promise<FromWebviewProtocol[T][1]> | FromWebviewProtocol[T][1];

type ToIdeOrWebviewFromCoreProtocol = ToIdeFromCoreProtocol & ToWebviewFromCoreProtocol;

export class VsCodeMessenger {
  // 监听来自webview的消息
  onWebview<T extends FromWebviewProtocolCommand>(
    command: T,
    handler: WebviewMessageHandler<T>
  ): void {
    this.webviewProtocol.on(command, handler);
  }
  // 监听来自core的消息
  onCore<T extends keyof ToIdeOrWebviewFromCoreProtocol>(
    command: T,
    handler: (message: any) => any
  ): void {
    this.inProcessMessenger.externalOn(command, handler);
  }
  // 监听来自core或者webview的消息
  onWebviewOrCore<T extends keyof ToIdeFromWebviewOrCoreProtocol>(
    command: T,
    handler: (
      message: Message<ToIdeFromWebviewOrCoreProtocol[T][0]>
    ) => Promise<ToIdeFromWebviewOrCoreProtocol[T][1]> | ToIdeFromWebviewOrCoreProtocol[T][1]
  ) {
    this.onWebview(command, handler);
    this.onCore(command, handler);
  }

  constructor(
    private readonly inProcessMessenger: InProcessMessenger<ToCoreProtocol, FromCoreProtocol>,
    private readonly webviewProtocol: VsCodeWebviewProtocol,
    private readonly ide: VsCodeIde,
    private gitUtil: GitUtil = new GitUtil()
  ) {
    // feature
    this.onWebview('feature/openSettings', () => {
      vscode.commands.executeCommand('CUCloudAICodeAssistant.settings');
    });
    this.onWebview('feature/openHelp', () => {
      vscode.commands.executeCommand('CUCloudAICodeAssistant.openHelp');
    });
    // 打开链接
    this.onWebview('feature/openUrl', async ({ payload }) => {
      await openUrl(payload.url);
    });

    // 监听分支切换
    this.gitUtil.onDidBranchCheckout((repo) => {
      this.inProcessMessenger.externalRequest("git/onBranchChange", undefined)
    })

    // log
    this.onWebview('log/info', ({ payload }) => {
      if (payload.channel === 'outputChannel') {
        webviewProtocol.showLog('info', payload);
      } else {
        Logger.info('[GUI] ', payload.message);
      }
    });
    this.onWebview('log/warn', ({ payload }) => {
      if (payload.channel === 'outputChannel') {
        webviewProtocol.showLog('info', payload);
      } else {
        Logger.warn('[GUI] ', payload.message);
      }
    });
    this.onWebview('log/error', ({ payload }) => {
      if (payload.channel === 'outputChannel') {
        webviewProtocol.showLog('info', { message: payload.message, error: payload.error });
      } else {
        Logger.error('[GUI] ', payload.message, payload.error);
      }
    });
    this.onWebview('log/debug', ({ payload }) => {
      webviewProtocol.showLog('debug', payload);
      Logger.debug('[GUI] ', payload.message);
    });

    // user
    this.onWebview('user/login', async () => {
      const ide = store.extention;
      try {
        await ide?.authentication?.login();
        const accessToken = CodeAssistantAuth.getAccessToken();
        const account = CodeAssistantAuth.getAccount();
        // 更新配置项
        await configuration.updateConfig('username', account?.name, true);
        infoPopup('登录成功');
        // 发送登录成功消息给webview
        const params = {
          accessToken,
          account
        };
        webviewProtocol.send('user/onLoginSuccess', params);
        store.extention?.core.invoke("user/onLoginSuccess", params)
      } catch (err) {
        webviewProtocol.send('user/onLoginFail', { error: err });
      }
    });

    this.onWebview('user/onLoginSuccess', ({ payload }) => {
      store.extention?.authentication.storeSessions(payload);
      store.extention?.core.invoke("user/onLoginSuccess", payload)
    });
    this.onWebview('user/logout', () => {
      vscode.commands.executeCommand('CUCloudAICodeAssistant.logout');
    });

    /** PASS THROUGH FROM WEBVIEW TO CORE AND BACK **/
    WEBVIEW_TO_CORE_PASS_THROUGH.forEach((command) => {
      this.onWebview(command, async (msg) => {
        return (await this.inProcessMessenger.externalRequest(command, msg.payload, msg.commandId)) as any
      })
    })

    /** PASS THROUGH FROM CORE TO WEBVIEW AND BACK **/
    CORE_TO_WEBVIEW_PASS_THROUGH.forEach((command) => {
      this.onCore(command, async (msg) => {
        return this.webviewProtocol.send(command, msg.payload, msg.commandId);
      });
    });

    // event
    this.onWebview('event/onAddIDEContext', async (msg) => {
      const { allowClearHistory, allowOpenHistory } = msg.payload || {};
      setAllowClearHistory(!!allowClearHistory);
      setAllowOpenHistory(!!allowOpenHistory);
    });

    // query
    // 获取IDE信息
    this.onWebviewOrCore("query/ideInfo", async () => {
      return await store.extention?.ide.getIdeInfo() as IdeInfo
    })
    // 获取git信息
    this.onWebviewOrCore("query/gitInfo", async () => {
      return await this.ide.getGitInfo()
    })

    // git
    this.onWebviewOrCore('git/getBranchName', ({ payload }) => {
      return this.ide.getBranch(payload.dir);
    });
    this.onWebviewOrCore('git/getRepoName', ({ payload }) => {
      return this.ide.getRepoName(payload.dir);
    });
    this.onWebviewOrCore('git/getDiff', ({ payload }) => {
      return this.ide.getDiff(payload.includeUnstaged);
    });
    this.onWebviewOrCore('exec/command', ({ payload }) => {
      return this.ide.executeCommand(payload.command, payload.directory);
    });
    this.onWebviewOrCore('git/getLastCommitMessage', ({ payload }) => {
      return this.ide.getLastCommitMessage(payload.dir);
    });

    this.onWebviewOrCore('config/get', ({ payload }) => {
      return this.ide.getIdeConfig(payload.key);
    });
    this.onWebviewOrCore('config/getAll', () => {
      return this.ide.getIdeAllConfig();
    });
    this.onWebviewOrCore('config/update', ({ payload }) => {
      this.ide.updateIdeConfig(payload.key, payload.value);
    });
    this.onWebviewOrCore('config/reload', () => {
      this.ide.reloadIdeConfig();
    });

    // file
    this.onWebviewOrCore('file/create', ({ payload }) => {
      this.ide.createFile(payload.language, payload.content);
    });
    this.onWebviewOrCore('file/open', ({ payload }) => {
      this.ide.openFile(payload.path, payload.range);
    });
    this.onWebviewOrCore('codeReview/goto', ({ payload }) => {
      this.ide.reviewFile(payload.id, payload.reviews);
    });
    this.onWebviewOrCore('codeReview/ignore', ({ payload }) => {
      this.ide.ignoreReviewFile(payload.id, payload.filePath);
    })
    this.onWebviewOrCore('codeReview/ignoreAll', () => {
      this.ide.ignoreAllReviewFile();
    })
    this.onWebviewOrCore('file/edit', ({ payload }) => {
      const { path, content, start, end } = payload || {};
      this.ide.editFile(path, content, start, end);
    });
    this.onWebviewOrCore('file/delete', ({ payload }) => {
      this.ide.deleteFile(payload.path);
    });
    this.onWebviewOrCore('file/readFile', ({ payload }) => {
      return this.ide.readFile(payload.path);
    });
    this.onWebviewOrCore('file/download', ({ payload }) => {
      downloadFile(payload.path);
    });
    this.onWebviewOrCore('file/readDirectory', ({ payload }) => {
      return this.ide.listDir(payload.dir, payload?.options);
    });
    this.onWebviewOrCore('file/searchResults', ({ payload }) => {
      return this.ide.getSearchResults(payload.query);
    });
    this.onWebviewOrCore('file/showDiff', async ({ payload }) => {
      const { path, content } = payload || {};

      if (path && content) {
        setStoreDiff(path, content)
        await store.extention?.diffManager?.writeDiff(path, content);
      }
    });
    this.onWebviewOrCore('file/acceptDiff', async ({ payload }) => {
      await store.extention?.diffManager?.acceptDiff(payload?.path);
    });
    this.onWebviewOrCore('file/rejectDiff', async ({ payload }) => {
      await store.extention?.diffManager?.acceptDiff(payload?.path);
    });

    this.onWebviewOrCore('query/currentFile', () => {
      return this.ide.getCurrentFile();
    });
    this.onWebviewOrCore('query/pathSep', () => {
      return this.ide.pathSep();
    });
    this.onWebviewOrCore('query/openFiles', () => {
      return this.ide.getOpenFiles();
    });
    this.onWebviewOrCore('query/listFolders', () => {
      return this.ide.listFolders();
    });
    this.onWebviewOrCore('query/lastModified', ({ payload }) => {
      return this.ide.getLastModified(payload.files);
    });
    this.onWebviewOrCore('query/workspaceDirs', () => {
      return this.ide.getWorkspaceDirs();
    });
    this.onWebviewOrCore('query/directoryTree', ({ payload }) => {
      return this.ide.getDirectoryTree(payload.dir, payload.prefix, payload?.options);
    });

    // 查看变更
    this.onWebviewOrCore('feature/showDiff', async (message) => {
      const { payload } = message;
      if (payload.codeInfo) {
        const { filePath, start, end } = payload.codeInfo || {};
        const range = new vscode.Range(
          new vscode.Position(start?.line, start.character), // 指定起始位置，这里假设是第 10 行开头
          new vscode.Position(end?.line, end.character) // 指定结束位置，与起始位置相同可以插入在该位置
        );
        const uri = vscode.Uri.file(filePath);
        const newContent = await getUpdatedContent(uri, range, payload.content);
        await store.extention?.diffManager?.writeDiff(filePath, newContent);
      }
    });
    // 插入代码
    this.onWebviewOrCore('feature/insertCode', ({ payload }) => {
      acceptCodeToEditor(payload.content, payload.codeInfo);
    });

    // 插入终端命令
    this.onWebviewOrCore('feature/insertToTerminal', async ({ payload }) => {
      return this.ide.runCommand(payload.command, payload?.shouldExecute);
    });
    // 创建终端
    this.onWebviewOrCore('feature/createTerminal', () => {
      this.ide.createTerminal();
    });
    // 取消编辑器选中
    this.onWebviewOrCore('feature/cancelEditorSelection', () => {
      cancelEditorSelection();
    });
    this.onWebviewOrCore('feature/showToast', ({ payload }) => {
      return this.ide.showToast(payload.type, payload.message, payload?.btnText);
    });
    // 获取文件错误信息
    this.onWebviewOrCore('feature/getErrors', ({ payload }) => {
      return this.ide.getErrors(payload.filePaths);
    });

    this.onWebviewOrCore("query/gotoDefinition", async (msg) => {
      return await this.ide.gotoDefinition(msg.payload.location);
    });

    this.onWebviewOrCore("query/readRangeInFile", async (msg) => {
      return await vscode.workspace
        .openTextDocument(msg.payload.filepath)
        .then((document) => {
          const start = new vscode.Position(0, 0);
          const end = new vscode.Position(5, 0);
          const range = new vscode.Range(start, end);

          const contents = document.getText(range);
          return contents;
        });
    });

    // 同步服务器时间
    this.onCore('event/onServerTimeDiffChange', ({ payload }) => {
      const diff = payload?.diff;
      store.timeBetweenClientAndServer = diff;
      store.extention?.sidebar?.webviewProtocol.send("event/onServerTimeDiffChange", {
        diff
      });
    })
    // 登录失效
    this.onCore('user/logout', () => {
      logout()
    })
    // 埋点消息
    this.onCore('analytics/capture', ({ payload }) => {
      const { operation, completionId, ...rest } = payload;

      vscode.commands.executeCommand(
        'CUCloudAICodeAssistant.autoCompletion',
        operation,
        { ...rest, track_id: completionId }
      );
    })

    // this.onWebviewOrCore('index/progress', ({ payload }) => {
    //   store.extention?.sidebar.webviewProtocol?.send("index/progress", payload)
    //   const { progress } = payload;
    //   if (progress === 1) {
    //     stopStatusBarLoading()
    //   } else {
    //     const text = `正在索引中... ${(progress * 100).toFixed(2)}%`;
    //     setupStatusBar(undefined, true, text)
    //   }
    // });
  }
}
